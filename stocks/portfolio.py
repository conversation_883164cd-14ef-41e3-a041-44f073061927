#!/usr/bin/env python3
"""投资组合管理器

集成交易系统和智能止损管理的综合投资组合管理模块。
提供仓位管理、风险控制、自动止损和交易执行等功能。
结合机器学习预测和相关性分析，实现智能化的投资组合管理。
"""

import logging
from datetime import datetime
from typing import Dict, List

import numpy as np

from core.broker import IBKRClient
from config import DEFAULT_CONFIG, IBKRConfig
from stocks.risk import PositionInfo, SmartStopLossSystem, StopLossOrder

logger = logging.getLogger(__name__)


class PortfolioManager:
    """
    管理投资组合仓位并集成智能止损系统
    """

    def __init__(self, bot, ibkr_config: IBKRConfig = None):
        self.bot = bot
        self.config = ibkr_config or DEFAULT_CONFIG
        self.stop_loss_system = SmartStopLossSystem(ibkr_config)
        self.ibkr_client = IBKRClient(self.config)

        # 投资组合跟踪
        self.position_history = {}
        self.performance_metrics = {}

    async def connect(self) -> bool:
        """连接到IBKR服务"""
        ibkr_connected = await self.ibkr_client.connect()
        stop_loss_connected = await self.stop_loss_system.connect()
        return ibkr_connected and stop_loss_connected

    def disconnect(self):
        """断开与IBKR服务的连接"""
        try:
            if hasattr(self.ibkr_client, "disconnect"):
                self.ibkr_client.disconnect()
        except Exception as e:
            logger.warning(f"Error disconnecting IBKR client: {e}")

        try:
            if hasattr(self.stop_loss_system, "disconnect"):
                self.stop_loss_system.disconnect()
        except Exception as e:
            logger.warning(f"Error disconnecting stop loss system: {e}")

        # 清理缓存数据以防止内存泄漏
        self.position_history.clear()
        self.performance_metrics.clear()

    def extract_positions_from_bot(
        self, market_data: Dict, correlation_data: Dict
    ) -> List[PositionInfo]:
        """
        从机器人中提取当前仓位并创建PositionInfo对象
        """
        positions = []

        for ticker, position_data in self.bot.portfolio.items():
            if ticker in market_data:
                market_info = market_data[ticker]

                # 获取该股票的相关性匹配
                correlation_matches = self._get_correlation_matches(
                    ticker, correlation_data
                )

                position = PositionInfo(
                    ticker=ticker,
                    units=position_data["units"],
                    purchase_price=position_data["purchase_price"],
                    current_price=market_info["current_price"],
                    sector=market_info.get("sector", "Unknown"),
                    industry=market_info.get("industry", "Unknown"),
                    trend_score=market_info.get("trend_score", 0.0),
                    predicted_price=market_info.get(
                        "predicted_price", market_info["current_price"]
                    ),
                    volatility=market_info.get("volatility", 0.2),
                    correlation_matches=correlation_matches,
                    purchase_date=position_data.get("purchase_date", datetime.now()),
                )
                positions.append(position)

                # 跟踪仓位历史
                if ticker not in self.position_history:
                    self.position_history[ticker] = []
                self.position_history[ticker].append(position)

        return positions

    def _get_correlation_matches(
        self, ticker: str, correlation_data: Dict
    ) -> List[str]:
        """获取给定股票代码的相关股票"""
        matches = []

        # 来自日常交易系统的相关性分析
        if "correlation_opportunities" in correlation_data:
            for opportunity in correlation_data["correlation_opportunities"]:
                # 检查股票代码是否在买入或卖出候选中
                buy_tickers = [
                    s["ticker"] for s in opportunity.get("buy_candidates", [])
                ]
                sell_tickers = [
                    s["ticker"] for s in opportunity.get("sell_candidates", [])
                ]

                if ticker in buy_tickers:
                    matches.extend(
                        [t for t in buy_tickers + sell_tickers if t != ticker]
                    )
                elif ticker in sell_tickers:
                    matches.extend(
                        [t for t in buy_tickers + sell_tickers if t != ticker]
                    )

        # 来自板块分组
        if "sector_groups" in correlation_data:
            for sector, stocks in correlation_data["sector_groups"].items():
                sector_tickers = [s["ticker"] for s in stocks]
                if ticker in sector_tickers:
                    matches.extend([t for t in sector_tickers if t != ticker])

        return list(set(matches))  # 去除重复项

    async def manage_portfolio_risk(
        self, market_data: Dict, correlation_data: Dict
    ) -> Dict:
        """
        Main function to manage portfolio risk using smart stop losses
        """
        logger.info("🛡️ 开始投资组合风险管理...")

        try:
            # 提取当前仓位
            positions = self.extract_positions_from_bot(market_data, correlation_data)

            if not positions:
                logger.info("无仓位需要管理")
                return {"status": "no_positions", "orders": []}

            logger.info(f"管理 {len(positions)} 个仓位")

            # 创建/更新止损订单
            stop_loss_orders = await self.stop_loss_system.create_stop_loss_orders(
                positions, correlation_data, market_data
            )

            # 更新现有订单
            await self.stop_loss_system.update_stop_loss_orders(
                positions, correlation_data, market_data
            )

            # 计算投资组合指标
            portfolio_metrics = self._calculate_portfolio_metrics(
                positions, market_data
            )

            # 生成风险报告
            risk_report = self._generate_risk_report(
                positions, stop_loss_orders, portfolio_metrics
            )

            logger.info("✅ 投资组合风险管理完成")
            return risk_report

        except Exception as e:
            logger.error(f"投资组合风险管理出错: {e}")
            return {"status": "error", "error": str(e)}

    def _calculate_portfolio_metrics(
        self, positions: List[PositionInfo], market_data: Dict
    ) -> Dict:
        """计算投资组合的绩效和风险指标"""
        if not positions:
            return {}

        total_value = sum(pos.current_price * pos.units for pos in positions)
        total_cost = sum(pos.purchase_price * pos.units for pos in positions)
        total_pnl = total_value - total_cost
        total_pnl_pct = (total_pnl / total_cost) * 100 if total_cost > 0 else 0

        # 仓位级别指标
        position_metrics = []
        for pos in positions:
            position_value = pos.current_price * pos.units
            position_cost = pos.purchase_price * pos.units
            position_pnl = position_value - position_cost
            position_pnl_pct = (
                (position_pnl / position_cost) * 100 if position_cost > 0 else 0
            )

            position_metrics.append(
                {
                    "ticker": pos.ticker,
                    "value": position_value,
                    "cost": position_cost,
                    "pnl": position_pnl,
                    "pnl_pct": position_pnl_pct,
                    "weight": (position_value / total_value) * 100
                    if total_value > 0
                    else 0,
                    "sector": pos.sector,
                    "trend_score": pos.trend_score,
                }
            )

        # 板块多元化
        sector_exposure = {}
        for pos in positions:
            sector = pos.sector
            position_value = pos.current_price * pos.units
            sector_exposure[sector] = sector_exposure.get(sector, 0) + position_value

        sector_weights = (
            {
                sector: (value / total_value) * 100
                for sector, value in sector_exposure.items()
            }
            if total_value > 0
            else {}
        )

        return {
            "total_value": total_value,
            "total_cost": total_cost,
            "total_pnl": total_pnl,
            "total_pnl_pct": total_pnl_pct,
            "position_count": len(positions),
            "position_metrics": position_metrics,
            "sector_weights": sector_weights,
            "largest_position_weight": max([pm["weight"] for pm in position_metrics])
            if position_metrics
            else 0,
            "avg_trend_score": np.mean([pos.trend_score for pos in positions]),
        }

    def _generate_risk_report(
        self,
        positions: List[PositionInfo],
        stop_loss_orders: Dict[str, StopLossOrder],
        portfolio_metrics: Dict,
    ) -> Dict:
        """生成综合风险报告"""

        # 风险评估
        risk_level = "LOW"
        risk_factors = []

        # 检查集中度风险
        if portfolio_metrics.get("largest_position_weight", 0) > 20:
            risk_level = "MEDIUM"
            risk_factors.append("High concentration in single position")

        # 检查板块集中度
        max_sector_weight = (
            max(portfolio_metrics.get("sector_weights", {}).values())
            if portfolio_metrics.get("sector_weights")
            else 0
        )
        if max_sector_weight > 40:
            risk_level = "HIGH" if risk_level != "HIGH" else risk_level
            risk_factors.append("High sector concentration")

        # 检查趋势一致性
        avg_trend = portfolio_metrics.get("avg_trend_score", 0)
        if avg_trend < -1.0:
            risk_level = "HIGH"
            risk_factors.append("Portfolio heavily below trend")

        # 检查止损覆盖率
        stop_loss_coverage = len(stop_loss_orders) / len(positions) if positions else 0
        if stop_loss_coverage < 0.8:
            risk_factors.append("Insufficient stop loss coverage")

        return {
            "status": "success",
            "timestamp": datetime.now(),
            "portfolio_metrics": portfolio_metrics,
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "stop_loss_orders": len(stop_loss_orders),
            "stop_loss_coverage": stop_loss_coverage,
            "positions": len(positions),
            "recommendations": self._generate_recommendations(
                risk_level, risk_factors, portfolio_metrics
            ),
        }

    def _generate_recommendations(
        self, risk_level: str, risk_factors: List[str], portfolio_metrics: Dict
    ) -> List[str]:
        """基于风险分析生成可行动建议"""
        recommendations = []

        if risk_level == "HIGH":
            recommendations.append("🚨 考虑减少仓位规模以管理风险")

        if "High concentration in single position" in risk_factors:
            recommendations.append("📊 通过减少最大仓位来实现多元化")

        if "High sector concentration" in risk_factors:
            recommendations.append("🏢 考虑跨板块多元化投资")

        if "Portfolio heavily below trend" in risk_factors:
            recommendations.append("📉 审查仓位 - 市场可能转向看跌")

        if "Insufficient stop loss coverage" in risk_factors:
            recommendations.append("🛡️ 确保所有仓位都有止损保护")

        # 基于绩效的建议
        if portfolio_metrics.get("total_pnl_pct", 0) > 15:
            recommendations.append("💰 考虑对强势股票获利了结")
        elif portfolio_metrics.get("total_pnl_pct", 0) < -10:
            recommendations.append("⚠️ 审查止损水平 - 考虑收紧")

        if not recommendations:
            recommendations.append("✅ 投资组合风险管理良好")

        return recommendations

    async def execute_risk_management_cycle(
        self, market_data: Dict, correlation_data: Dict
    ) -> Dict:
        """
        Execute a complete risk management cycle
        """
        logger.info("🔄 开始风险管理周期...")

        # 连接到服务
        if not await self.connect():
            return {"status": "connection_failed"}

        try:
            # 管理投资组合风险
            risk_report = await self.manage_portfolio_risk(
                market_data, correlation_data
            )

            # 记录摘要
            if risk_report.get("status") == "success":
                metrics = risk_report["portfolio_metrics"]
                logger.info("📊 投资组合摘要:")
                logger.info(f"  总价值: ${metrics.get('total_value', 0):,.2f}")
                logger.info(
                    f"  总盈亏: ${metrics.get('total_pnl', 0):,.2f} ({metrics.get('total_pnl_pct', 0):.1f}%)"
                )
                logger.info(f"  风险等级: {risk_report['risk_level']}")
                logger.info(f"  止损覆盖率: {risk_report['stop_loss_coverage']:.1%}")

            return risk_report

        finally:
            self.disconnect()

    def get_portfolio_summary(self) -> Dict:
        """获取当前投资组合摘要"""
        return {
            "positions": len(self.bot.portfolio),
            "capital": self.bot.capital,
            "invested": self.bot.invested,
            "uninvested": self.bot.uninvested,
            "active_stop_losses": len(self.stop_loss_system.active_orders),
            "last_updated": datetime.now(),
        }
