#!/usr/bin/env python
"""
IBKR数据提供器

高性能历史数据下载模块，提供IBKR API的并发数据获取功能。
支持速率限制、智能重试、数据缓存、增量更新和预取策略。
包含数据质量过滤、进度跟踪和断点续传等高级功能。
"""

import asyncio
import logging
import os
import pickle
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from core.broker import get_ibkr_client
from core.cache import cache_config, filter_stocks_by_quality
from config import DEFAULT_CONFIG
# 向后兼容
from config import IBKRConfig
from core.exceptions import (
    IBKRConnectionError,
    DataValidationError,
    TradingSystemError,
    handle_errors,
    async_handle_errors,
    OperationResult
)

logger = logging.getLogger(__name__)


class IBKRDataProvider:
    """
    IBKR历史数据的高性能并发数据提供器

    功能特性:
    - 遵守IBKR速率限制 (最大50个并发，每10分钟60个请求)
    - 自动重试和指数退避
    - 进度跟踪和报告
    - 优化为下载数千只股票
    """

    def __init__(
        self,
        config: IBKRConfig = None,
        max_concurrent: int = 1,  # 默认顺序执行以适应IBKR速率限制
        use_multiple_connections: bool = False,
    ):
        self.config = config or DEFAULT_CONFIG.ibkr
        self.max_concurrent = 1  # 使用顺序执行以更好地管理速率限制
        self.use_multiple_connections = use_multiple_connections

        # 主连接 - 使用统一的客户端管理器
        self.ibkr_client = get_ibkr_client("data", self.config)
        self.connected = False

        # 多连接以提高吞吐量（实验性）
        self.additional_clients = []
        self.client_pool_size = 3 if use_multiple_connections else 1

        # 速率限制（每个连接）
        self.request_times = []  # 跟踪请求时间以进行速率限制
        self.max_requests_per_10min = 60

        # Advanced rate limiting
        self.connection_request_times = {}  # Track per-connection requests

        # Statistics
        self.successful_downloads = 0
        self.failed_downloads = 0
        self.start_time = None

        # Data farm health monitoring
        self.last_successful_request = time.time()
        self.consecutive_failures = 0
        self.data_farm_inactive_count = 0

        # Resume/checkpoint functionality - use new cache structure
        self.progress_file = cache_config.get_progress_file_path("download")
        self.failed_symbols_file = cache_config.get_progress_file_path("failed_symbols")
        self.completed_symbols = set()
        self.failed_symbols = set()
        self.invalid_symbols = set()  # Symbols that don't exist (Error 200)

        # Performance optimizations
        self.data_cache = {}  # Cache recent downloads
        self.cache_ttl = 300  # 5 minutes cache TTL
        self.batch_size = 50  # Optimal batch size for requests

        # Data persistence and incremental updates - use new cache structure
        self.data_store_path = cache_config.get_cache_path("stock_data")
        self.incremental_data_file = "incremental_data.pkl"
        self.last_update_file = "last_update.txt"
        self._ensure_data_store_exists()

        # Pre-fetch scheduling
        self.prefetch_enabled = True
        self.market_hours = {"start": 9.5, "end": 16.0}  # 9:30 AM  # 4:00 PM

        # Advanced performance tracking
        self.request_latencies = []
        self.optimal_concurrent = max_concurrent

    def _ensure_data_store_exists(self):
        """确保数据存储目录存在"""

        if not os.path.exists(self.data_store_path):
            os.makedirs(self.data_store_path)

    def _is_market_hours(self) -> bool:
        """检查当前时间是否在交易时间内 (EST)"""
        from datetime import datetime

        import pytz

        try:
            est = pytz.timezone("US/Eastern")
            now_est = datetime.now(est)
            current_hour = now_est.hour + now_est.minute / 60.0

            # Check if it's a weekday and during market hours
            is_weekday = now_est.weekday() < 5  # Monday = 0, Friday = 4
            is_market_time = (
                self.market_hours["start"] <= current_hour <= self.market_hours["end"]
            )

            return is_weekday and is_market_time
        except Exception:
            # If timezone handling fails, assume it's market hours
            return True

    def _should_prefetch(self) -> bool:
        """判断是否应该预取数据 (非交易时间)"""
        return self.prefetch_enabled and not self._is_market_hours()

    def _get_last_update_time(self) -> Optional[datetime]:
        """获取最后数据更新的时间戳"""

        last_update_path = os.path.join(self.data_store_path, self.last_update_file)

        if os.path.exists(last_update_path):
            try:
                with open(last_update_path, "r") as f:
                    timestamp_str = f.read().strip()
                    return datetime.fromisoformat(timestamp_str)
            except Exception:
                return None
        return None

    def _update_last_update_time(self):
        """更新最后数据更新的时间戳"""

        last_update_path = os.path.join(self.data_store_path, self.last_update_file)

        with open(last_update_path, "w") as f:
            f.write(datetime.now().isoformat())

    def _load_incremental_data(self) -> Optional[Dict]:
        """加载之前存储的增量数据"""

        data_path = os.path.join(self.data_store_path, self.incremental_data_file)

        if os.path.exists(data_path):
            try:
                with open(data_path, "rb") as f:
                    return pickle.load(f)
            except Exception:
                logger.warning("无法加载增量数据，将下载新数据")
                return None
        return None

    def _save_incremental_data(self, data: Dict):
        """保存数据用于增量更新"""

        data_path = os.path.join(self.data_store_path, self.incremental_data_file)

        try:
            with open(data_path, "wb") as f:
                pickle.dump(data, f)
            self._update_last_update_time()
            logger.info(
                f"Saved incremental data for {len(data.get('tickers', []))} stocks"
            )
        except Exception as e:
            logger.error(f"无法保存增量数据: {e}")

    def _needs_update(self, hours_threshold: int = 24) -> bool:
        """根据数据年龄检查是否需要更新"""
        last_update = self._get_last_update_time()

        if last_update is None:
            return True

        age_hours = (datetime.now() - last_update).total_seconds() / 3600
        return age_hours >= hours_threshold

    async def connect(self) -> bool:
        """连接到IBKR"""
        try:
            await self.ibkr_client.connect()
            self.connected = True
            self.last_successful_request = time.time()  # Reset success timer
            self.consecutive_failures = 0  # Reset failure counter
            logger.info(
                "Connected to IBKR for sequential downloading (optimized for data farm stability)"
            )
            return True
        except Exception as e:
            logger.error(f"无法连接到IBKR: {e}")
            self.connected = False
            return False

    async def disconnect(self):
        """断开与IBKR的连接"""
        try:
            if self.connected and self.ibkr_client:
                if hasattr(self.ibkr_client, "disconnect"):
                    if asyncio.iscoroutinefunction(self.ibkr_client.disconnect):
                        await self.ibkr_client.disconnect()
                    else:
                        self.ibkr_client.disconnect()
                self.connected = False
                logger.info("已从 IBKR 断开连接")
        except Exception as e:
            logger.error(f"断开连接时出错: {e}")
            self.connected = False

    async def _rate_limit_check(self):
        """顺序下载的简化速率限制"""
        # For sequential downloads, we handle rate limiting at the higher level
        # Just add a small delay to be gentle on the API
        await asyncio.sleep(0.1)

    async def _check_data_farm_health(self) -> bool:
        """检查历史数据农场是否活跃和健康"""
        try:
            # Check if too much time has passed since last successful request
            time_since_success = time.time() - self.last_successful_request

            if time_since_success > 300:  # 5 minutes without success
                logger.warning(
                    f"⚠️  No successful requests for {time_since_success:.0f}s - data farm may be inactive"
                )
                return False

            if self.consecutive_failures >= 5:
                logger.warning(
                    f"⚠️  {self.consecutive_failures} consecutive failures - data farm may be inactive"
                )
                return False

            return True

        except Exception as e:
            logger.error(f"检查数据场状态错误: {e}")
            return False

    async def _reconnect_data_farm(self) -> bool:
        """重新连接到IBKR以刷新数据农场连接"""
        try:
            logger.info("🔄 重连以刷新数据场连接...")

            # Disconnect current connection
            if self.connected:
                await self.disconnect()
                await asyncio.sleep(2)  # Wait before reconnecting

            # Reconnect
            success = await self.connect()

            if success:
                logger.info("✅ 数据场重连成功")
                self.consecutive_failures = 0
                self.data_farm_inactive_count += 1
                self.last_successful_request = time.time()

                # Wait a bit longer after reconnection to let data farm stabilize
                await asyncio.sleep(5)
                return True
            else:
                logger.error("❌ 数据场重连失败")
                return False

        except Exception as e:
            logger.error(f"数据场重连过程中错误: {e}")
            return False

    def _is_data_farm_error(self, error_message: str) -> bool:
        """检查错误是否与数据农场非活跃状态相关"""
        error_lower = error_message.lower()
        data_farm_keywords = [
            "historical market data farm connection is inactive",
            "farm connection is inactive",
            "data farm",
            "farm is inactive",
            "historical data farm",
            "market data farm",
            "farm connection",
            "hfarm",
            "hmds",
            "historical market data",
        ]

        return any(keyword in error_lower for keyword in data_farm_keywords)

    def _save_progress(self, all_symbols: List[str], current_index: int):
        """将下载进度保存到文件"""
        try:
            import json

            progress_data = {
                "all_symbols": all_symbols,
                "current_index": current_index,
                "completed_symbols": list(self.completed_symbols),
                "failed_symbols": list(self.failed_symbols),
                "invalid_symbols": list(self.invalid_symbols),
                "timestamp": time.time(),
                "total_symbols": len(all_symbols),
                "successful_downloads": self.successful_downloads,
                "failed_downloads": self.failed_downloads,
            }

            with open(self.progress_file, "w") as f:
                json.dump(progress_data, f, indent=2)

            logger.debug(
                f"Progress saved: {current_index}/{len(all_symbols)} symbols processed"
            )

        except Exception as e:
            logger.warning(f"保存进度失败: {e}")

    def _load_progress(self) -> dict:
        """从文件加载下载进度"""
        try:
            import json
            import os

            if not os.path.exists(self.progress_file):
                return None

            with open(self.progress_file, "r") as f:
                progress_data = json.load(f)

            # Load sets
            self.completed_symbols = set(progress_data.get("completed_symbols", []))
            self.failed_symbols = set(progress_data.get("failed_symbols", []))
            self.invalid_symbols = set(progress_data.get("invalid_symbols", []))

            # Restore counters
            self.successful_downloads = progress_data.get("successful_downloads", 0)
            self.failed_downloads = progress_data.get("failed_downloads", 0)

            logger.info(
                f"📂 进度已加载: {len(self.completed_symbols)} 已完成, {len(self.failed_symbols)} 失败, {len(self.invalid_symbols)} 无效"
            )

            return progress_data

        except Exception as e:
            logger.warning(f"加载进度失败: {e}")
            return None

    def _clear_progress(self):
        """成功完成后清除进度文件"""
        try:
            import os

            for file in [self.progress_file, self.failed_symbols_file]:
                if os.path.exists(file):
                    os.remove(file)
                    logger.info(f"清除进度文件: {file}")
        except Exception as e:
            logger.warning(f"清除进度文件失败: {e}")

    def _is_symbol_invalid(self, error_message: str) -> bool:
        """检查错误是否表示无效/不存在的代码"""
        error_lower = error_message.lower()
        invalid_keywords = [
            "error 200",
            "未找到此请求的证券定义",
            "no security definition",
            "contract not found",
            "invalid contract",
        ]
        return any(keyword in error_lower for keyword in invalid_keywords)

    async def _download_single_stock(
        self,
        ticker: str,
        start_date: str,
        end_date: str,
        semaphore: asyncio.Semaphore,
        retry_count: int = 3,
        duration: str = None,
        bar_size: str = "1 day",
        what_to_show: str = "ADJUSTED_LAST",
    ) -> Optional[Tuple]:
        """使用高级优化下载单只股票数据"""

        async with semaphore:  # Limit concurrent requests
            request_start_time = time.time()

            for attempt in range(retry_count):
                try:
                    # Advanced rate limiting
                    await self._rate_limit_check()

                    # Calculate duration if not provided
                    if duration is None:
                        # Calculate duration based on start and end dates
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                        days_diff = (end_dt - start_dt).days

                        if days_diff > 365:
                            duration = f"{days_diff} D"  # Use days for long periods
                        else:
                            duration = f"{days_diff} D"

                    # Download data using IBKR client with specified parameters
                    df = await self.ibkr_client.get_historical_data(
                        symbol=ticker,
                        duration=duration,
                        bar_size=bar_size,
                        what_to_show=what_to_show,
                    )

                    # Track request latency for adaptive control
                    request_latency = time.time() - request_start_time
                    self.request_latencies.append(request_latency)

                    # Keep only recent latencies for adaptive control
                    if len(self.request_latencies) > 50:
                        self.request_latencies = self.request_latencies[-50:]

                    if df is not None and not df.empty:
                        # Validate data quality before processing
                        if len(df) < 183:  # Minimum data requirement for correlation analysis
                            logger.warning(
                                f"⚠️ {ticker}: Insufficient data ({len(df)} days, need 183+)"
                            )
                            self.failed_downloads += 1
                            return None

                        # Check for missing values
                        if (
                            df["close"].isna().sum() > len(df) * 0.1
                        ):  # More than 10% missing
                            logger.warning(
                                f"⚠️ {ticker}: Too many missing values ({df['close'].isna().sum()}/{len(df)})"
                            )
                            self.failed_downloads += 1
                            return None

                        # Check for zero or negative prices
                        if (df["close"] <= 0).any():
                            logger.warning(
                                f"⚠️ {ticker}: Invalid price data (zero/negative values)"
                            )
                            self.failed_downloads += 1
                            return None

                        # Extract data from DataFrame
                        dates = df.index.tolist()
                        prices = df["close"].tolist()
                        volumes = df["volume"].tolist()

                        result = (ticker, dates, prices, volumes)

                        # Store in cache for future use
                        self._store_cache(ticker, start_date, end_date, result)

                        self.successful_downloads += 1
                        self.consecutive_failures = 0  # Reset failure counter
                        self.last_successful_request = (
                            time.time()
                        )  # Update success time

                        # Enhanced progress reporting
                        if self.successful_downloads % 50 == 0:  # More frequent updates
                            elapsed = time.time() - self.start_time
                            rate = self.successful_downloads / elapsed
                            avg_latency = sum(self.request_latencies[-10:]) / min(
                                10, len(self.request_latencies)
                            )
                            logger.info(
                                f"📈 Progress: {self.successful_downloads} stocks, {rate:.1f}/sec, avg latency: {avg_latency:.2f}s"
                            )

                        return result
                    else:
                        logger.warning(f"No data received for {ticker}")
                        self.failed_downloads += 1
                        self.consecutive_failures += 1
                        return None

                except Exception as e:
                    self.consecutive_failures += 1
                    error_str = str(e).lower()

                    # Check for invalid symbol errors (don't retry these)
                    if self._is_symbol_invalid(str(e)):
                        logger.warning(f"❌ Invalid symbol {ticker}: {e}")
                        self.invalid_symbols.add(ticker)
                        self.failed_downloads += 1
                        return None

                    # Check for data farm related errors using dedicated method
                    if self._is_data_farm_error(str(e)):
                        logger.warning(f"🚨 Data farm issue detected for {ticker}: {e}")

                        # Try to reconnect data farm
                        if await self._reconnect_data_farm():
                            logger.info(
                                f"🔄 Retrying {ticker} after data farm reconnection..."
                            )
                            continue  # Retry this attempt
                        else:
                            logger.error(
                                f"❌ Data farm reconnection failed for {ticker}"
                            )

                    if attempt < retry_count - 1:
                        # Adaptive backoff based on error type
                        if "pacing" in error_str:
                            wait_time = 5 + (
                                2**attempt
                            )  # Longer wait for pacing violations
                        elif any(
                            keyword in error_str
                            for keyword in ["farm", "inactive", "timeout"]
                        ):
                            wait_time = 10 + (
                                2**attempt
                            )  # Even longer wait for data farm issues
                        else:
                            wait_time = (2**attempt) + np.random.uniform(0, 1)

                        logger.warning(
                            f"Attempt {attempt + 1} failed for {ticker}: {e}. Retrying in {wait_time:.1f}s..."
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(
                            f"Failed to download {ticker} after {retry_count} attempts: {e}"
                        )
                        self.failed_downloads += 1
                        return None

        return None

    async def _download_incremental_update(
        self, tickers: List[str], existing_data: Dict
    ) -> Dict:
        """只下载自上次更新以来的新数据"""
        last_update = self._get_last_update_time()

        if last_update is None:
            logger.info("No previous data found, performing full download")
            return await self.download_multiple_stocks(tickers)

        # Calculate how many days of new data we need
        days_since_update = (datetime.now() - last_update).days

        if days_since_update == 0:
            logger.info("Data is current, using existing data")
            return existing_data

        logger.info(
            f"Performing incremental update for {days_since_update} days of new data"
        )

        # Download only recent data for each ticker
        new_data = {
            "tickers": [],
            "dates": [],
            "price": [],
            "volume": [],
            "currencies": [],
            "sectors": {},
            "industries": {},
            "exchange_rates": {"USD": []},
            "default_currency": "USD",
        }

        # Use a smaller duration for incremental updates
        duration = f"{min(days_since_update + 1, 30)} D"  # Max 30 days

        successful_updates = 0

        for ticker in tickers:
            try:
                # Download recent data
                df = await self.ibkr_client.get_historical_data(
                    symbol=ticker, duration=duration, bar_size="1 day"
                )

                if df is not None and not df.empty:
                    # Get only the new data points
                    new_dates = df.index[df.index > last_update]

                    if len(new_dates) > 0:
                        new_df = df.loc[new_dates]

                        dates = new_df.index.tolist()
                        prices = new_df["close"].tolist()
                        volumes = new_df["volume"].tolist()

                        new_data["tickers"].append(ticker)
                        new_data["price"].append(prices)
                        new_data["volume"].append(volumes)
                        new_data["currencies"].append("USD")
                        new_data["sectors"][ticker] = existing_data["sectors"].get(
                            ticker, "Unknown"
                        )
                        new_data["industries"][ticker] = existing_data[
                            "industries"
                        ].get(ticker, "Unknown")

                        # Store dates from first successful download
                        if not new_data["dates"]:
                            new_data["dates"] = dates

                        successful_updates += 1

                # Rate limiting for incremental updates
                await self._rate_limit_check()

            except Exception as e:
                logger.warning(f"Failed to update {ticker}: {e}")
                continue

        if successful_updates > 0:
            # Merge new data with existing data
            merged_data = self._merge_incremental_data(existing_data, new_data)
            logger.info(
                f"Successfully updated {successful_updates} stocks with incremental data"
            )
            return merged_data
        else:
            logger.warning("No incremental updates available, using existing data")
            return existing_data

    def _merge_incremental_data(self, existing_data: Dict, new_data: Dict) -> Dict:
        """将新增量数据与现有数据合并"""
        if not new_data["tickers"]:
            return existing_data

        merged_data = existing_data.copy()

        # For each ticker with new data, append the new prices/volumes
        for i, ticker in enumerate(new_data["tickers"]):
            if ticker in existing_data["tickers"]:
                # Find the index of this ticker in existing data
                ticker_idx = existing_data["tickers"].index(ticker)

                # Append new prices and volumes
                existing_prices = list(existing_data["price"][ticker_idx])
                existing_volumes = list(existing_data["volume"][ticker_idx])

                new_prices = new_data["price"][i]
                new_volumes = new_data["volume"][i]

                merged_data["price"][ticker_idx] = existing_prices + new_prices
                merged_data["volume"][ticker_idx] = existing_volumes + new_volumes

        # Update dates to include new dates
        if new_data["dates"]:
            existing_dates = list(existing_data["dates"])
            new_dates = new_data["dates"]

            # Combine and sort dates, removing duplicates
            all_dates = existing_dates + new_dates
            unique_dates = sorted(list(set(all_dates)))
            merged_data["dates"] = pd.DatetimeIndex(unique_dates)

        return merged_data

    def _ensure_stock_classifications(self, data: Dict) -> Dict:
        """确保股票分类数据完整"""
        try:
            # 检查是否有stock_info.csv文件
            import os

            import pandas as pd

            stock_info_path = "data/stock_info/stock_info.csv"
            if os.path.exists(stock_info_path):
                logger.info(
                    "📊 Loading stock classifications from data/stock_info/stock_info.csv"
                )
                stock_info_df = pd.read_csv(stock_info_path)

                # 更新sectors和industries
                for _, row in stock_info_df.iterrows():
                    ticker = row["symbol"]
                    if ticker in data.get("tickers", []):
                        data["sectors"][ticker] = row.get("sector", "Unknown")
                        data["industries"][ticker] = row.get("industry", "Unknown")

                logger.info(
                    f"✅ Updated classifications for {len(stock_info_df)} stocks"
                )
            else:
                logger.warning(
                    "⚠️  data_store/stock_info.csv not found, using default classifications"
                )
                # 为所有股票设置默认分类
                for ticker in data.get("tickers", []):
                    if ticker not in data.get("sectors", {}):
                        data["sectors"][ticker] = "Unknown"
                    if ticker not in data.get("industries", {}):
                        data["industries"][ticker] = "Unknown"

        except Exception as e:
            logger.error(f"Error loading stock classifications: {e}")
            # 设置默认分类
            for ticker in data.get("tickers", []):
                data.setdefault("sectors", {})[ticker] = "Unknown"
                data.setdefault("industries", {})[ticker] = "Unknown"

        return data

    def _load_legacy_data_pickle(self) -> Dict:
        """加载遗留的data.pickle文件"""
        try:
            import os
            import pickle

            pickle_path = "data.pickle"
            if os.path.exists(pickle_path):
                logger.info("📦 Loading data from legacy data.pickle file...")
                with open(pickle_path, "rb") as f:
                    data = pickle.load(f)

                # 验证数据结构
                if isinstance(data, dict) and "tickers" in data:
                    logger.info(
                        f"✅ Successfully loaded {len(data.get('tickers', []))} stocks from data.pickle"
                    )

                    # 确保所有必需的字段存在
                    required_fields = [
                        "tickers",
                        "dates",
                        "price",
                        "volume",
                        "currencies",
                        "sectors",
                        "industries",
                    ]
                    for field in required_fields:
                        if field not in data:
                            if field in ["sectors", "industries"]:
                                data[field] = {}
                            else:
                                data[field] = []

                    # 确保exchange_rates和default_currency存在
                    if "exchange_rates" not in data:
                        data["exchange_rates"] = {"USD": []}
                    if "default_currency" not in data:
                        data["default_currency"] = "USD"

                    return data
                else:
                    logger.error("❌ Invalid data structure in data.pickle")
                    return {}
            else:
                logger.info("📦 No data.pickle file found")
                return {}

        except Exception as e:
            logger.error(f"❌ Error loading data.pickle: {e}")
            return {}

    def _adaptive_concurrent_control(self):
        """根据性能动态调整并发请求"""
        if len(self.request_latencies) < 10:
            return self.max_concurrent

        # Calculate average latency for last 10 requests
        recent_latencies = self.request_latencies[-10:]
        avg_latency = sum(recent_latencies) / len(recent_latencies)

        # Adjust concurrent requests based on latency (more conservative)
        if avg_latency < 1.0:  # Very fast responses, can increase concurrency slightly
            self.optimal_concurrent = min(self.optimal_concurrent + 1, 20)
        elif avg_latency > 3.0:  # Slow responses, reduce concurrency significantly
            self.optimal_concurrent = max(self.optimal_concurrent - 3, 5)

        return self.optimal_concurrent

    def _check_cache(self, ticker: str, start: str, end: str) -> Optional[Tuple]:
        """检查缓存中是否有数据"""
        cache_key = f"{ticker}_{start}_{end}"
        if cache_key in self.data_cache:
            cached_data, timestamp = self.data_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return cached_data
            else:
                # Remove expired cache
                del self.data_cache[cache_key]
        return None

    def _store_cache(self, ticker: str, start: str, end: str, data: Tuple):
        """将数据存储在缓存中"""
        cache_key = f"{ticker}_{start}_{end}"
        self.data_cache[cache_key] = (data, time.time())

    async def prefetch_data(self, tickers: List[str]) -> bool:
        """在非交易时间预取数据，以便在交易时更快访问"""
        if not self._should_prefetch():
            logger.info("Prefetch skipped - market is open or prefetch disabled")
            return False

        logger.info(
            f"🌙 Starting data prefetch for {len(tickers)} stocks (non-market hours)"
        )

        try:
            # 交易系统总是需要最新数据
            logger.info(
                "📥 Trading system downloading fresh market data for accurate analysis"
            )
            updated_data = await self.download_multiple_stocks(tickers)

            # Save the prefetched data
            self._save_incremental_data(updated_data)

            logger.info(
                f"✅ Prefetch completed for {len(updated_data.get('tickers', []))} stocks"
            )
            return True

        except Exception as e:
            logger.error(f"❌ Prefetch failed: {e}")
            return False

    async def get_data_with_prefetch(
        self, tickers: List[str], start: str = None, end: str = None
    ) -> Dict:
        """使用智能预取和增量更新策略获取数据"""

        # First, try to load existing data
        existing_data = self._load_incremental_data()

        # Check if existing data covers all requested tickers
        if existing_data:
            existing_tickers = set(existing_data.get("tickers", []))
            requested_tickers = set(tickers)
            missing_tickers = requested_tickers - existing_tickers

            # 交易系统必须使用最新数据 - 始终下载新数据
            logger.info(
                "🔄 Trading system requires fresh market data - downloading latest prices..."
            )

        # Always download fresh data for all requested tickers to ensure completeness
        logger.info(f"📊 Downloading fresh data for {len(tickers)} stocks")
        fresh_data = await self.download_multiple_stocks(tickers, start, end)
        self._save_incremental_data(fresh_data)
        return fresh_data

    async def download_multiple_stocks(
        self,
        tickers: List[str],
        start: str = None,
        end: str = None,
        max_concurrent: int = None,
        duration: str = None,
        bar_size: str = "1 day",
        what_to_show: str = "ADJUSTED_LAST",
    ) -> Dict:
        """
        并发下载多只股票的历史数据

        参数:
            tickers: 股票代码列表
            start: 开始日期 (YYYY-MM-DD格式)
            end: 结束日期 (YYYY-MM-DD格式)
            max_concurrent: 覆盖默认的最大并发请求数

        返回:
            包含股票数据的字典，格式与原始download函数相同
        """

        # Use provided max_concurrent or default
        if max_concurrent is not None:
            self.max_concurrent = min(max_concurrent, 50)

        # Set default dates
        if not end:
            end = datetime.now().strftime("%Y-%m-%d")
        if not start:
            start = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        # Check cache for already downloaded data
        cached_results = []
        remaining_tickers = []

        for ticker in tickers:
            cached_data = self._check_cache(ticker, start, end)
            if cached_data:
                cached_results.append(cached_data)
                logger.debug(f"Using cached data for {ticker}")
            else:
                remaining_tickers.append(ticker)

        logger.info("🚀 开始并发下载")
        logger.info(
            f"📊 股票总数: {len(tickers)} (已缓存: {len(cached_results)}, 下载中: {len(remaining_tickers)})"
        )
        logger.info(f"⚡ 自适应并发限制: {self._adaptive_concurrent_control()}")
        logger.info(f"📅 日期范围: {start} 到 {end}")

        self.start_time = time.time()
        self.successful_downloads = len(cached_results)  # Count cached as successful
        self.failed_downloads = 0

        # Use adaptive concurrent control
        optimal_concurrent = self._adaptive_concurrent_control()
        asyncio.Semaphore(optimal_concurrent)

        # Create tasks only for remaining stocks (not cached)
        # Note: We'll handle tasks in the sequential execution section below

        # Load previous progress if available
        progress_data = self._load_progress()
        start_index = 0

        if progress_data and progress_data.get("all_symbols") == tickers:
            start_index = progress_data.get("current_index", 0)
            logger.info(
                f"🔄 Resuming download from symbol {start_index}/{len(tickers)}"
            )
            logger.info(
                f"📊 Previous progress: {len(self.completed_symbols)} completed, {len(self.failed_symbols)} failed"
            )
        else:
            logger.info(f"🆕 Starting fresh download of {len(tickers)} symbols")
            self.completed_symbols.clear()
            self.failed_symbols.clear()
            self.invalid_symbols.clear()

        # Filter out already completed symbols and create remaining tasks
        remaining_tickers = []
        remaining_tasks = []

        for i, ticker in enumerate(tickers):
            if i < start_index or ticker in self.completed_symbols:
                # For completed symbols, try to load from progress data
                if ticker in self.completed_symbols:
                    # Create a mock result for completed symbols
                    # This will be handled by loading from incremental data
                    pass
                continue  # Skip already processed symbols
            remaining_tickers.append(ticker)
            # Create a dummy semaphore for sequential downloads
            dummy_semaphore = asyncio.Semaphore(1)
            remaining_tasks.append(
                self._download_single_stock(
                    ticker,
                    start,
                    end,
                    dummy_semaphore,
                    3,
                    duration,
                    bar_size,
                    what_to_show,
                )
            )

        if remaining_tasks:
            logger.info(
                f"⏳ Executing {len(remaining_tasks)} sequential downloads (resuming from {start_index})..."
            )
            logger.info(
                f"📊 Remaining: {len(remaining_tasks)}, Completed: {len(self.completed_symbols)}, Failed: {len(self.failed_symbols)}"
            )

            # Ensure we're still connected before starting downloads
            if not self.connected:
                logger.warning("Connection lost, attempting to reconnect...")
                if not await self.connect():
                    logger.error("Failed to reconnect, aborting downloads")
                    return {
                        "tickers": [],
                        "dates": [],
                        "price": [],
                        "volume": [],
                        "currencies": [],
                        "sectors": {},
                        "industries": {},
                        "exchange_rates": {"USD": []},
                        "default_currency": "USD",
                    }
        elif len(self.completed_symbols) > 0:
            # All data is already completed, try to load from incremental data or legacy data.pickle
            logger.info(
                "✅ All data available from previous downloads, loading from incremental data..."
            )
            existing_data = self._load_incremental_data()
            if existing_data and len(existing_data.get("tickers", [])) > 0:
                # Ensure stock classification data is loaded
                existing_data = self._ensure_stock_classifications(existing_data)
                logger.info(
                    f"📦 Loaded {len(existing_data['tickers'])} stocks from incremental data"
                )
                return existing_data
            else:
                # Try to load from legacy data.pickle file
                logger.info(
                    "⚠️  No incremental data found, trying to load from legacy data.pickle..."
                )
                legacy_data = self._load_legacy_data_pickle()
                if legacy_data and len(legacy_data.get("tickers", [])) > 0:
                    # Ensure stock classification data is loaded
                    legacy_data = self._ensure_stock_classifications(legacy_data)
                    logger.info(
                        f"📦 Loaded {len(legacy_data['tickers'])} stocks from legacy data.pickle"
                    )
                    # Save as incremental data for future use
                    self._save_incremental_data(legacy_data)
                    return legacy_data
                else:
                    logger.warning(
                        "⚠️  No legacy data found either, will proceed with empty cache"
                    )

        # Initialize new_results for all cases
        new_results = []

        if remaining_tasks:
            # Sequential execution with smart rate limiting
            request_count = 0
            start_time = time.time()

            logger.info("🚀 开始顺序下载，具有优化限速和断点续传支持")
            logger.info("📊 Target: 60 requests per minute for maximum speed")

            for i, task in enumerate(remaining_tasks):
                current_ticker = remaining_tickers[i]
                current_global_index = start_index + i

                # Smart rate limit management - check every 55 requests to be safe
                if request_count >= 55:
                    elapsed = time.time() - start_time
                    if elapsed < 60:  # If less than 1 minute has passed
                        wait_time = (
                            60 - elapsed + 1
                        )  # Wait until next minute + small buffer
                        logger.info(
                            f"⏱️  Rate limit management: waiting {wait_time:.1f}s after {request_count} requests"
                        )
                        await asyncio.sleep(wait_time)

                    # Reset counters
                    request_count = 0
                    start_time = time.time()

                # Check data farm health every 100 requests
                if (i + 1) % 100 == 0:
                    if not await self._check_data_farm_health():
                        logger.warning(
                            "🚨 Data farm health check failed - attempting reconnection..."
                        )
                        if await self._reconnect_data_farm():
                            logger.info(
                                "✅ Data farm reconnection successful, continuing..."
                            )
                        else:
                            logger.error(
                                "❌ Data farm reconnection failed, but continuing..."
                            )

                # Execute single task
                try:
                    result = await task
                    new_results.append(result)
                    request_count += 1

                    # Track completion status
                    if result is not None:
                        self.completed_symbols.add(current_ticker)
                    else:
                        self.failed_symbols.add(current_ticker)

                    # Save progress every 10 symbols
                    if (i + 1) % 10 == 0:
                        self._save_progress(tickers, current_global_index + 1)

                    # Progress reporting every 50 stocks
                    if (i + 1) % 50 == 0:
                        elapsed_total = (
                            time.time() - self.start_time if self.start_time else 0
                        )
                        rate = (i + 1) / elapsed_total if elapsed_total > 0 else 0
                        remaining = len(remaining_tasks) - (i + 1)
                        eta_seconds = remaining / rate if rate > 0 else 0
                        eta_hours = eta_seconds / 3600

                        total_completed = len(self.completed_symbols)
                        total_failed = len(self.failed_symbols)
                        total_invalid = len(self.invalid_symbols)

                        logger.info(
                            f"📈 Sequential progress: {i + 1}/{len(remaining_tasks)} remaining"
                        )
                        logger.info(
                            f"📊 Overall: {total_completed} completed, {total_failed} failed, {total_invalid} invalid"
                        )
                        logger.info(
                            f"⚡ Rate: {rate:.2f} stocks/sec, ETA: {eta_hours:.1f} hours"
                        )

                        # Also report data farm health
                        if self.data_farm_inactive_count > 0:
                            logger.info(
                                f"🔄 Data farm reconnections: {self.data_farm_inactive_count}"
                            )

                except Exception as e:
                    new_results.append(e)
                    request_count += 1

                    # Check if it's an invalid symbol error
                    if self._is_symbol_invalid(str(e)):
                        self.invalid_symbols.add(current_ticker)
                        logger.warning(f"❌ Invalid symbol: {current_ticker}")
                    else:
                        self.failed_symbols.add(current_ticker)

                    # Save progress even on errors
                    if (i + 1) % 10 == 0:
                        self._save_progress(tickers, current_global_index + 1)

                # Optimal delay between requests (1.0 seconds = 60 requests/minute)
                await asyncio.sleep(1.0)

            # Save final progress
            self._save_progress(tickers, len(tickers))
        else:
            logger.info("✅ All data available from cache!")

        # Combine cached and new results
        results = cached_results + [r for r in new_results if r is not None]

        # Process results
        all_data = {
            "tickers": [],
            "dates": [],
            "price": [],
            "volume": [],
            "currencies": [],
            "sectors": {},
            "industries": {},
            "exchange_rates": {"USD": []},
            "default_currency": "USD",
        }

        for result in results:
            if isinstance(result, Exception):
                continue

            if result is None:
                continue

            ticker, dates, prices, volumes = result

            # Store data
            all_data["tickers"].append(ticker)
            all_data["price"].append(prices)
            all_data["volume"].append(volumes)
            all_data["currencies"].append("USD")
            all_data["sectors"][ticker] = "Unknown"  # Will be filled later
            all_data["industries"][ticker] = "Unknown"  # Will be filled later

            # Store dates from first successful download
            if not all_data["dates"]:
                all_data["dates"] = dates

        # Convert to numpy arrays
        if len(all_data["tickers"]) > 0:
            try:
                all_data["price"] = np.array(all_data["price"])
                all_data["volume"] = np.array(all_data["volume"])
                all_data["dates"] = pd.DatetimeIndex(all_data["dates"])

                # Create exchange rates (all USD for now)
                all_data["exchange_rates"]["USD"] = [1.0] * len(all_data["dates"])
            except Exception as e:
                logger.warning(f"Error converting data to arrays: {e}")
                # Keep data as lists if conversion fails
                pass

        # Final statistics
        elapsed_time = time.time() - self.start_time
        success_rate = self.successful_downloads / len(tickers) * 100
        download_rate = (
            self.successful_downloads / elapsed_time if elapsed_time > 0 else 0
        )

        logger.info("🎯 顺序下载完成！")
        logger.info(f"✅ 成功: {self.successful_downloads}")
        logger.info(f"❌ 失败: {self.failed_downloads}")
        logger.info(f"🚫 无效代码: {len(self.invalid_symbols)}")
        logger.info(f"📈 成功率: {success_rate:.1f}%")
        logger.info(f"⚡ 下载速率: {download_rate:.1f} 股票/秒")
        logger.info(f"⏱️  总耗时: {elapsed_time:.1f} 秒")

        # Report data farm health statistics
        if self.data_farm_inactive_count > 0:
            logger.info(f"🔄 数据农场重连次数: {self.data_farm_inactive_count}")
            logger.info("✅ 数据农场问题已自动处理")

        # Report resume statistics
        if len(self.completed_symbols) > 0:
            logger.info(
                f"📂 恢复功能: 已为 {len(self.completed_symbols)} 个代码保存进度"
            )

        # Clear progress files if download completed successfully
        if self.successful_downloads > 0 and len(self.failed_symbols) == 0:
            logger.info("🧹 下载成功完成 - 清理进度文件")
            self._clear_progress()
        else:
            logger.info(f"💾 进度已保存 - 可恢复 {len(self.failed_symbols)} 个失败代码")

        # Report invalid symbols for cleanup
        if len(self.invalid_symbols) > 0:
            logger.warning(
                f"🚫 Found {len(self.invalid_symbols)} invalid symbols that should be removed from symbol list:"
            )
            for symbol in sorted(list(self.invalid_symbols))[:10]:  # Show first 10
                logger.warning(f"   - {symbol}")
            if len(self.invalid_symbols) > 10:
                logger.warning(f"   ... and {len(self.invalid_symbols) - 10} more")

        # Report data farm health statistics
        if self.data_farm_inactive_count > 0:
            logger.info(f"🔄 Data farm reconnections: {self.data_farm_inactive_count}")
            logger.info("✅ Data farm issues were automatically handled")

        # Apply data quality filtering based on duration
        if len(all_data["tickers"]) > 0:
            # Calculate duration in years from start and end dates
            try:
                start_dt = datetime.strptime(start, "%Y-%m-%d")
                end_dt = datetime.strptime(end, "%Y-%m-%d")
                duration_years = max(1, round((end_dt - start_dt).days / 365))
            except Exception:
                duration_years = 1  # Default to 1 year if parsing fails

            logger.info(
                f"🔍 Applying data quality filter for {duration_years}-year duration..."
            )
            original_count = len(all_data["tickers"])

            # Filter stocks by data quality
            filtered_data, quality_report = filter_stocks_by_quality(
                all_data, duration_years
            )

            # Print quality report
            cache_config.print_quality_report(quality_report)

            # Use filtered data
            all_data = filtered_data

            logger.info(
                f"📊 Data quality filtering: {original_count} → {len(all_data['tickers'])} stocks"
            )

        # Save incremental data for future use
        if len(all_data["tickers"]) > 0:
            logger.info(
                f"💾 Saving incremental data for {len(all_data['tickers'])} stocks..."
            )

            # Save to new cache structure
            duration_years = getattr(
                self, "_current_duration_years", 3
            )  # Default to 3 years
            cache_path = cache_config.get_stock_data_cache_path(duration_years)

            try:
                import pickle

                with open(cache_path, "wb") as f:
                    pickle.dump(all_data, f)
                logger.info(f"💾 Saved stock data cache to {cache_path}")
            except Exception as e:
                logger.error(f"Failed to save stock data cache: {e}")

            # Also save to legacy location for backward compatibility
            self._save_incremental_data(all_data)

        return all_data

    # Legacy methods for backward compatibility
    @async_handle_errors(reraise=False)
    async def download_stock_data(
        self, ticker: str, start: str = None, end: str = None
    ) -> OperationResult[pd.DataFrame]:
        """下载单只股票数据 (旧版兼容性)"""
        if not ticker:
            return OperationResult.error_result(
                DataValidationError("股票代码不能为空")
            )

        if not self.ibkr_client:
            return OperationResult.error_result(
                IBKRConnectionError("IBKR客户端未连接")
            )

        data = await self.ibkr_client.get_historical_data(
            symbol=ticker, duration="1 Y", bar_size="1 day"
        )

        if data is None or data.empty:
            return OperationResult.error_result(
                DataValidationError(f"未获取到{ticker}的数据")
            )

        return OperationResult.success_result(data)

    def get_stock_info_from_ibkr(self, ticker: str) -> Dict[str, str]:
        """获取股票行业/板块信息 (目前为占位符)"""
        return {"sector": "Unknown", "industry": "Unknown"}

    async def get_real_time_prices(self, tickers: List[str]) -> Dict[str, float]:
        """获取多个股票代码的实时价格"""
        prices = {}

        if self.connected:
            for ticker in tickers:
                try:
                    price = await self.ibkr_client.get_current_price(ticker)
                    if price:
                        prices[ticker] = price
                except Exception as e:
                    logger.error(f"Error getting real-time price for {ticker}: {e}")

        return prices


# Enhanced async wrapper function with prefetch and incremental updates
async def download_with_ibkr(
    tickers: list,
    start: Union[str, int] = None,
    end: Union[str, int] = None,
    config: IBKRConfig = None,
    use_prefetch: bool = True,
) -> dict:
    """
    使用IBKR下载历史数据，Yahoo Finance作为后备方案

    该函数保持与现有download()函数的兼容性
    """
    provider = IBKRDataProvider(config)

    try:
        # Connect to IBKR
        if not await provider.connect():
            print("⚠️  IBKR connection failed")
            raise Exception(
                "IBKR connection failed - please check IB Gateway is running"
            )

        # Convert timestamps to date strings if needed
        if isinstance(start, int):
            start = datetime.fromtimestamp(start).strftime("%Y-%m-%d")
        if isinstance(end, int):
            end = datetime.fromtimestamp(end).strftime("%Y-%m-%d")

        # Use intelligent prefetch strategy if enabled, but force sequential download
        if use_prefetch:
            data = await provider.get_data_with_prefetch(tickers, start, end)
        else:
            # Force sequential download for better rate limit management
            data = await provider.download_multiple_stocks(
                tickers, start, end, max_concurrent=1
            )

        return data

    except Exception as e:
        print(f"IBKR download failed: {e}")
        raise Exception(f"IBKR download failed: {e}")
    finally:
        # Keep connection alive during download, disconnect only at the end
        if provider.connected:
            await provider.disconnect()


# Synchronous wrapper for backward compatibility
def download_ibkr_sync(
    tickers: list,
    start: Union[str, int] = None,
    end: Union[str, int] = None,
    config: IBKRConfig = None,
) -> dict:
    """IBKR下载的同步包装器"""
    return asyncio.run(download_with_ibkr(tickers, start, end, config))


# Main download function (replaces download.py)
def download(
    tickers: list,
    start: Union[str, int] = None,
    end: Union[str, int] = None,
    interval: str = "1d",
) -> dict:
    """
    使用优化的IBKR API下载股票代码历史数据，支持预取和增量更新。

    参数
    ----------
    tickers: list
        需要下载历史信息的股票代码列表。
    start: str or int
        开始下载数据的日期。
    end: str or int
        结束下载数据的日期。
    interval: str
        数据频率 (目前只支持 "1d")。

    返回
    -------
    data: dict
        包含以下键的字典:
        - tickers: 股票代码列表
        - dates: 日期的pandas DatetimeIndex
        - price: 调整后收盘价数组, shape=(股票数, 时间长度)
        - volume: 成交量数组, shape=(股票数, 时间长度)
        - currencies: 每个股票代码的货币列表
        - exchange_rates: 汇率字典
        - default_currency: 默认货币 (USD)
        - sectors: 每个股票代码的行业字典
        - industries: 每个股票代码的板块字典
    """

    print("Using optimized IBKR data source with prefetch and incremental updates...")
    try:
        return download_ibkr_sync(tickers, start, end, DEFAULT_CONFIG)
    except Exception as e:
        print(f"IBKR download failed: {e}")
        print("Please ensure IB Gateway is running and API is enabled")
        raise e


def get_exchange_rates(
    from_currencies: list, to_currency: str, dates: pd.Index
) -> dict:
    """
    使用IBKR API获取汇率。

    参数
    ----------
    from_currencies: list
        需要转换的货币列表。
    to_currency: str
        目标转换货币。
    dates: pd.Index
        需要汇率的日期。

    返回
    -------
    xrates: dict
        以货币为键，以指定日期汇率列表为值的字典。
    """
    # For simplicity, assume all currencies are USD or return 1.0 exchange rate
    # In a real implementation, you would use IBKR to get forex data

    default_rate = [1.0] * len(dates)
    xrates = {}

    for curr in set(from_currencies):
        if curr == to_currency or curr in ["USD", "Unknown", None]:
            xrates[curr] = default_rate
        else:
            # For now, use 1.0 as default
            # TODO: Implement IBKR forex data retrieval
            xrates[curr] = default_rate
            logger.debug(f"Using default exchange rate 1.0 for {curr} to {to_currency}")

    return xrates
