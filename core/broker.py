#!/usr/bin/env python
"""
IBKR API客户端封装器

提供Interactive Brokers API的高级封装，支持异步连接、历史数据获取、
实时价格查询、订单管理和账户信息查询等功能。
包含连接管理、错误处理和数据转换等核心功能。
"""

import asyncio
import logging
import os
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import nest_asyncio
import pandas as pd

# 导入 ib_insync 并处理错误
try:
    from ib_insync import IB, LimitOrder, MarketOrder, Option, Stock, StopOrder, util
    from ib_insync.contract import Contract
    from ib_insync.order import Order

    # 配置ib_insync日志级别，过滤信息性消息和合约警告
    util.logToConsole(logging.ERROR)  # 只显示ERROR及以上级别，过滤WARNING

    IB_INSYNC_AVAILABLE = True
except ImportError as e:
    print(f"Warning: ib_insync not available: {e}")
    print("Please install with: pip install ib_insync")
    IB_INSYNC_AVAILABLE = False

    # 创建虚拟类以防止导入错误
    class IB:
        pass

    class Stock:
        pass

    class MarketOrder:
        pass

    class LimitOrder:
        pass

    class Contract:
        pass

    class Order:
        pass

    util = None

from config import DEFAULT_CONFIG, IBKRConfig

# 启用嵌套事件循环以兼容 Jupyter
nest_asyncio.apply()


class IBKRClientManager:
    """IBKR客户端管理器 - 单例模式，避免重复创建连接"""

    _instance = None
    _clients = {}  # 存储不同用途的客户端实例

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(IBKRClientManager, cls).__new__(cls)
        return cls._instance

    def get_client(self, purpose: str = "default", config=None) -> 'IBKRClient':
        """
        获取指定用途的客户端实例

        Args:
            purpose: 客户端用途 ('default', 'trading', 'data', 'options', 'updater')
            config: IBKR配置，如果为None则使用默认配置

        Returns:
            IBKRClient实例
        """
        if purpose not in self._clients:
            # 为不同用途分配不同的客户端ID范围，避免冲突
            client_id_base = {
                'default': 1000,
                'trading': 2000,
                'data': 3000,
                'options': 4000,
                'updater': 5000
            }

            # 创建配置副本并设置专用客户端ID
            if config is None:
                from config import DEFAULT_CONFIG
                config = DEFAULT_CONFIG.ibkr

            # 创建配置副本
            client_config = type(config)(
                host=config.host,
                port=config.port,
                client_id=client_id_base.get(purpose, 1000) + random.randint(1, 99),
                timeout=getattr(config, "timeout", 30),
                paper_trading=config.paper_trading,
                max_position_size=getattr(config, "max_position_size", 10000.0),
                max_daily_trades=getattr(config, "max_daily_trades", 50),
                stop_loss_pct=getattr(config, "stop_loss_pct", 0.05),
                take_profit_pct=getattr(config, "take_profit_pct", 0.10),
            )

            self._clients[purpose] = IBKRClient(client_config)

        return self._clients[purpose]

    def disconnect_all(self):
        """断开所有客户端连接"""
        for purpose, client in self._clients.items():
            if client.connected:
                client.disconnect()
        self._clients.clear()

    def get_connected_clients(self) -> Dict[str, 'IBKRClient']:
        """获取所有已连接的客户端"""
        return {purpose: client for purpose, client in self._clients.items()
                if client.connected}


class IBKRClient:
    """Interactive Brokers API 客户端封装器"""

    def __init__(self, config: IBKRConfig = None):
        self.config = config or DEFAULT_CONFIG

        # 如果未指定则自动分配客户端ID
        if self.config.client_id is None:
            # 使用时间戳 + 随机数确保唯一性
            self.config.client_id = int(str(int(time.time()))[-3:]) + random.randint(
                1, 99
            )

        self.ib = IB()
        self.connected = False
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """设置日志配置 - 使用现有的根日志器配置"""
        logger = logging.getLogger("IBKRClient")
        logger.setLevel(getattr(logging, self.config.log_level))

        # 如果根日志器已配置处理器则不添加处理器
        # 这可以防止重复的日志文件并尊重主系统的日志配置
        root_logger = logging.getLogger()
        if root_logger.handlers:
            # 使用现有的根日志器配置
            return logger

        # 后备方案：仅在没有根配置时添加处理器
        if not logger.handlers:
            # 在有组织的目录结构中创建日志文件
            date_str = datetime.now().strftime("%Y%m%d")
            log_dir = f"results/{date_str}/logs"

            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)

            log_file = f"{log_dir}/ibkr_trading.log"

            handler = logging.FileHandler(log_file)
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

            # 同时记录到控制台
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        return logger

    def _validate_config(self) -> bool:
        """验证 IBKR 配置参数"""
        try:
            # 检查主机
            if not self.config.host or not isinstance(self.config.host, str):
                self.logger.error("无效的主机配置")
                return False

            # 检查端口
            if not isinstance(self.config.port, int) or self.config.port <= 0:
                self.logger.error(f"无效的端口配置: {self.config.port}")
                return False

            # 验证常见的 IBKR 端口
            valid_ports = [
                7496,
                7497,
                4001,
                4002,
            ]  # 实盘, 纸上交易, 网关实盘, 网关纸上交易
            if self.config.port not in valid_ports:
                self.logger.warning(
                    f"Unusual port {self.config.port}, expected one of {valid_ports}"
                )

            # 如果指定了超时时间则检查
            if hasattr(self.config, "timeout"):
                if (
                    not isinstance(self.config.timeout, (int, float))
                    or self.config.timeout <= 0
                ):
                    self.logger.error(
                        f"Invalid timeout configuration: {self.config.timeout}"
                    )
                    return False

            return True

        except Exception as e:
            self.logger.error(f"配置验证错误: {e}")
            return False

    async def connect(self) -> bool:
        """连接到 TWS/IB Gateway"""
        if not IB_INSYNC_AVAILABLE:
            self.logger.error("ib_insync不可用 - 无法连接到IBKR")
            return False

        # 验证配置
        if not self._validate_config():
            return False

        try:
            self.ib = IB()

            # 强化的客户端ID生成策略 - 避免冲突

            # 使用更大的随机数范围和更复杂的组合算法
            process_id = os.getpid() % 10000  # 扩大到4位进程ID
            timestamp = int(time.time()) % 1000000  # 使用6位时间戳
            base_random = random.randint(100000, 999999)  # 6位随机数

            # 添加微秒级时间戳增加唯一性
            microsecond = int(time.time() * 1000000) % 1000

            # 组合生成高度唯一的客户端ID
            if self.config.client_id:
                dynamic_client_id = self.config.client_id
            else:
                # 使用更复杂的算法确保唯一性
                dynamic_client_id = (
                    (timestamp % 10000) * 100000 +  # 时间戳的4位
                    (process_id % 100) * 1000 +     # 进程ID的2位
                    (base_random % 1000)            # 随机数的3位
                )

            # 确保客户端ID在有效范围内 (1-2147483647)
            dynamic_client_id = max(1, min(dynamic_client_id, 2147483647))

            # 强化的连接重试机制 - 处理客户端ID冲突
            max_attempts = 20  # 增加重试次数
            connection_successful = False

            for attempt in range(max_attempts):
                try:
                    # 确保之前的连接完全断开
                    if hasattr(self, 'ib') and self.ib and self.ib.isConnected():
                        self.ib.disconnect()
                        await asyncio.sleep(1)  # 等待断开完成

                    self.logger.info(
                        f"🔌 Connecting to {self.config.host}:{self.config.port} with clientId {dynamic_client_id} (attempt {attempt + 1}/{max_attempts})"
                    )

                    # 设置更长的连接超时
                    timeout = getattr(self.config, "timeout", 30)
                    await self.ib.connectAsync(
                        host=self.config.host,
                        port=self.config.port,
                        clientId=dynamic_client_id,
                        timeout=timeout,
                    )

                    # 连接成功，保存使用的客户端ID
                    self.config.client_id = dynamic_client_id
                    connection_successful = True
                    break  # 成功，退出重试循环

                except Exception as e:
                    error_str = str(e).lower()
                    is_client_id_conflict = (
                        "already in use" in error_str
                        or "326" in error_str
                        or "客户号码已被使用" in error_str
                        or "clientid" in error_str
                        or "peer closed connection" in error_str
                    )

                    if is_client_id_conflict:
                        # 生成全新的客户端ID，使用更强的随机性
                        new_timestamp = int(time.time() * 1000) % 1000000
                        new_random = random.randint(100000, 999999)
                        new_microsecond = int(time.time() * 1000000) % 1000

                        dynamic_client_id = (
                            (new_timestamp % 10000) * 100000 +
                            (process_id % 100) * 1000 +
                            (new_random % 1000) +
                            new_microsecond
                        )
                        dynamic_client_id = max(1, min(dynamic_client_id, 2147483647))

                        self.logger.warning(
                            f"⚠️ Client ID conflict detected: {e}"
                        )
                        self.logger.info(
                            f"🔄 Generating new client ID {dynamic_client_id} (attempt {attempt + 1}/{max_attempts})"
                        )

                        if attempt < max_attempts - 1:
                            # 递增等待时间，给Gateway更多时间清理连接
                            wait_time = min(3 + attempt * 2, 20)
                            self.logger.info(f"⏳ Waiting {wait_time}s before retry...")
                            await asyncio.sleep(wait_time)
                            continue
                    else:
                        # 非客户端ID冲突错误，直接抛出
                        self.logger.error(f"❌ Connection failed with non-ID error: {e}")
                        raise e

                    # 如果已达到最大重试次数
                    if attempt == max_attempts - 1:
                        self.logger.error(
                            f"❌ Failed to connect after {max_attempts} attempts. Last error: {e}"
                        )
                        raise e

            if not connection_successful:
                raise Exception("Failed to establish connection after all retry attempts")
            self.connected = True
            self.logger.info(
                f"✅ Successfully connected to IBKR at {self.config.host}:{self.config.port} with clientId {dynamic_client_id}"
            )

            # 等待连接稳定并验证连接状态
            await asyncio.sleep(2)

            # 验证连接是否真正建立
            if not self.ib.isConnected():
                self.logger.error(
                    "Connection verification failed - not actually connected"
                )
                self.connected = False
                return False

            # 设置事件处理器以过滤零仓位
            self.ib.positionEvent.clear()  # 清除默认处理程序
            self.ib.updatePortfolioEvent.clear()  # 清除默认处理程序
            self.ib.positionEvent += self._on_position_update
            self.ib.updatePortfolioEvent += self._on_portfolio_update

            # 添加错误处理器 - 清除默认处理器并添加自定义处理器
            self.ib.errorEvent.clear()  # 清除默认错误处理器
            self.ib.errorEvent += self._on_error
            self.logger.info("✅ Custom error handler registered")

            # 设置市场数据类型，避免冲突
            try:
                if self.config.paper_trading:
                    # 纸上交易使用延迟数据，避免与真实账户冲突
                    self.ib.reqMarketDataType(3)  # 延迟数据
                    self.logger.info("📊 Using delayed market data for paper trading")
                else:
                    # 真实交易尝试实时数据
                    try:
                        self.ib.reqMarketDataType(1)  # 实时数据
                        self.logger.info("📊 Using real-time market data")
                    except Exception as e:
                        self.ib.reqMarketDataType(3)  # 降级到延迟数据
                        self.logger.warning(f"⚠️ Fallback to delayed market data: {e}")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to set market data type: {e}")

            # 检查是否为纸上交易并获取账户信息
            try:
                account_summary = self.ib.accountSummary()
                if account_summary:
                    account_type = next(
                        (
                            item.value
                            for item in account_summary
                            if item.tag == "AccountType"
                        ),
                        "Unknown",
                    )
                    self.logger.info(f"账户类型: {account_type}")

                # 获取管理的账户列表
                accounts = self.ib.managedAccounts()
                if accounts:
                    self.logger.info(f"管理账户: {accounts}")
                else:
                    self.logger.warning("未找到管理账户")

            except Exception as e:
                self.logger.warning(f"无法获取账户信息: {e}")

            return True

        except Exception as e:
            self.logger.error(f"无法连接到IBKR: {e}")
            self.connected = False

            # 清理连接状态
            try:
                if hasattr(self, "ib") and self.ib and self.ib.isConnected():
                    self.ib.disconnect()
            except Exception as cleanup_error:
                self.logger.warning(f"连接清理时出错: {cleanup_error}")

            return False

    def _on_position_update(self, position):
        """处理仓位更新 - 只记录非零仓位"""
        if position.position != 0:
            self.logger.info(
                f"Position update: {position.contract.symbol} = {position.position} shares"
            )

    def _on_portfolio_update(self, portfolio_item):
        """处理投资组合更新 - 只记录非零仓位"""
        if portfolio_item.position != 0:
            self.logger.info(
                f"Portfolio update: {portfolio_item.contract.symbol} = {portfolio_item.position} shares, "
                f"value=${portfolio_item.marketValue:.2f}"
            )

    def _on_error(self, reqId, errorCode, errorString):
        """处理IBKR错误事件"""
        self.logger.debug(f"🔍 Error handler called: {errorCode} - {errorString}")

        if errorCode == 10197:
            # 市场数据冲突 - 自动切换到延迟数据
            self.logger.warning(f"⚠️ Market data conflict (Error 10197): {errorString}")
            try:
                self.ib.reqMarketDataType(3)  # 切换到延迟数据
                self.logger.info("🔄 Switched to delayed market data to resolve conflict")
            except Exception as e:
                self.logger.error(f"❌ Failed to switch market data type: {e}")
        elif errorCode == 200:
            # 合约未找到 - 正常业务逻辑，使用DEBUG级别
            self.logger.debug(f"📋 Contract not found (Error 200): {errorString}")
        elif errorCode in [2104, 2106, 2158]:
            # 市场数据农场连接状态 - 信息性消息，使用DEBUG级别
            self.logger.debug(f"📊 Market data farm status (Info {errorCode}): {errorString}")
        elif errorCode == 2107:
            # 历史数据农场非活跃状态 - 正常状态，使用DEBUG级别
            self.logger.debug(f"📊 Historical data farm inactive (Info {errorCode}): {errorString}")
        elif errorCode == 2119:
            # 正在连接市场数据农场 - 连接过程信息，使用DEBUG级别
            self.logger.debug(f"📊 Connecting to market data farm (Info {errorCode}): {errorString}")
        elif errorCode == 2108:
            # 市场数据农场连接暂未激活但可按需提供 - 正常状态，使用DEBUG级别
            self.logger.debug(f"📊 Market data farm inactive but available (Info {errorCode}): {errorString}")
        elif errorCode in [326, 327]:
            # 客户端ID冲突 - 已在连接逻辑中处理
            self.logger.error(f"❌ Client ID conflict (Error {errorCode}): {errorString}")
        elif errorCode in [1100, 1101, 1102]:
            # 连接状态变化 - 重要信息
            self.logger.warning(f"🔌 Connection status (Info {errorCode}): {errorString}")
        else:
            # 其他错误
            if errorCode >= 1000:
                # 系统错误
                self.logger.error(f"❌ IBKR System Error {errorCode}: {errorString}")
            else:
                # 一般错误
                self.logger.warning(f"⚠️ IBKR Warning {errorCode}: {errorString}")

    def disconnect(self):
        """从TWS/IB Gateway断开连接 - 强化版本确保完全清理"""
        if hasattr(self, 'ib') and self.ib:
            try:
                if self.ib.isConnected():
                    self.logger.info("🔌 Disconnecting from IBKR...")
                    self.ib.disconnect()
                    # 给一些时间让连接完全清理
                    import time
                    time.sleep(1)
                self.connected = False
                self.logger.info("✅ 成功从 IBKR 断开连接")
            except Exception as e:
                self.logger.warning(f"⚠️ Error during disconnect: {e}")
                self.connected = False
        else:
            self.connected = False

    def create_stock_contract(self, symbol: str, exchange: str = "SMART") -> Stock:
        """创建股票合约"""
        return Stock(symbol, exchange, "USD")

    async def validate_option_contract(self, contract):
        """验证期权合约是否有效"""
        try:
            if not self.connected:
                self.logger.error("❌ Not connected to IBKR")
                return None

            qualified = await self.ib.qualifyContractsAsync(contract)
            if qualified:
                self.logger.debug(f"✅ Valid option contract: {qualified[0]}")
                return qualified[0]
            else:
                self.logger.warning(f"❌ Invalid option contract: {contract}")
                return None
        except Exception as e:
            self.logger.error(f"❌ Contract validation failed: {e}")
            return None

    def create_option_contract(self, symbol: str, expiry: str, strike: float, right: str, exchange: str = "SMART"):
        """创建期权合约（使用SMART交易所避免Error 200）"""
        from ib_insync import Option
        return Option(symbol, expiry, strike, right, exchange, "USD")

    async def get_historical_data(
        self,
        symbol: str,
        duration: str = "1 Y",
        bar_size: str = "1 day",
        what_to_show: str = "ADJUSTED_LAST",
    ) -> Optional[pd.DataFrame]:
        """
        Get historical data for a symbol

        参数:
        -----------
        symbol: str
            股票代码
        duration: str
            持续时间字符串 (例如: '1 Y', '6 M', '30 D')
        bar_size: str
            K线大小 (例如: '1 day', '1 hour', '5 mins')
        what_to_show: str
            显示什么数据 (例如: 'ADJUSTED_LAST', 'TRADES', 'MIDPOINT')
        """
        try:
            contract = self.create_stock_contract(symbol)

            # 验证合约
            await self.ib.qualifyContractsAsync(contract)

            # 请求历史数据
            bars = await self.ib.reqHistoricalDataAsync(
                contract,
                endDateTime="",
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=True,
                formatDate=1,
            )

            if not bars:
                self.logger.warning(f"{symbol} 未接收到历史数据")
                return None

            # 转换为DataFrame
            df = util.df(bars)
            df.set_index("date", inplace=True)
            df.index = pd.to_datetime(df.index)

            self.logger.info(f"为 {symbol} 获取了 {len(df)} 个K线")
            return df

        except Exception as e:
            self.logger.error(f"获取 {symbol} 历史数据时出错: {e}")
            return None

    async def place_market_order(
        self, symbol: str, quantity: int, action: str = "BUY"
    ) -> Optional[Order]:
        """
        Place a market order

        参数:
        -----------
        symbol: str
            股票代码
        quantity: int
            Number of shares
        action: str
            'BUY' or 'SELL'
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            await self.ib.qualifyContractsAsync(contract)

            order = MarketOrder(action, quantity)
            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} market order for {quantity} shares of {symbol}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing market order for {symbol}: {e}")
            return None

    async def place_limit_order(
        self, symbol: str, quantity: int, limit_price: float, action: str = "BUY"
    ) -> Optional[Order]:
        """
        Place a limit order

        参数:
        -----------
        symbol: str
            股票代码
        quantity: int
            Number of shares
        limit_price: float
            Limit price
        action: str
            'BUY' or 'SELL'
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            await self.ib.qualifyContractsAsync(contract)

            order = LimitOrder(action, quantity, limit_price)
            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} limit order for {quantity} shares of {symbol} at ${limit_price}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing limit order for {symbol}: {e}")
            return None

    def get_account_summary(self) -> Dict:
        """获取账户摘要信息"""
        try:
            summary = self.ib.accountSummary()
            account_info = {}

            for item in summary:
                account_info[item.tag] = item.value

            return account_info

        except Exception as e:
            self.logger.error(f"Error getting account summary: {e}")
            return {}

    async def get_contract_details(self, symbol: str) -> Optional[List]:
        """获取代码的详细合约信息"""
        if not self.connected:
            return None

        try:
            # 创建合约
            contract = Stock(symbol, "SMART", "USD")

            # 获取合约详情
            details = self.ib.reqContractDetails(contract)

            if details:
                return details
            else:
                self.logger.debug(f"No contract details found for {symbol}")
                return None

        except Exception as e:
            self.logger.debug(f"获取 {symbol} 合约详情失败: {e}")
            return None

    def get_portfolio(self) -> List[Dict]:
        """获取当前投资组合仓位 (只显示非零仓位)"""
        if not self.connected:
            return []

        try:
            positions = self.ib.positions()
            portfolio = []

            for position in positions:
                if position.position != 0:  # 仅包含非零仓位
                    portfolio.append(
                        {
                            "symbol": position.contract.symbol,
                            "position": position.position,
                            "avgCost": position.avgCost,
                            "currency": position.contract.currency,
                            "exchange": position.contract.exchange,
                        }
                    )

            return portfolio

        except Exception as e:
            self.logger.error(f"Error getting portfolio: {e}")
            return []

    async def get_positions(self) -> List:
        """从IBKR获取所有当前仓位"""
        if not self.connected:
            return []

        try:
            # 获取所有仓位 - 使用正确的ib_insync API
            positions = self.ib.positions()
            return positions

        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    async def get_option_details(self, contract) -> Optional[Dict]:
        """获取包括Greeks在内的详细期权信息"""
        if not self.connected:
            return None

        try:
            # 请求期权市场数据
            ticker = self.ib.reqMktData(contract, "", False, False)
            await asyncio.sleep(2)  # 等待数据

            if ticker:
                # 获取期权价格和希腊字母
                option_details = {
                    "price": ticker.marketPrice() or ticker.close or 0.0,
                    "bid": ticker.bid or 0.0,
                    "ask": ticker.ask or 0.0,
                    "volume": ticker.volume or 0,
                    "delta": getattr(ticker, "delta", 0.0),
                    "gamma": getattr(ticker, "gamma", 0.0),
                    "theta": getattr(ticker, "theta", 0.0),
                    "vega": getattr(ticker, "vega", 0.0),
                    "iv": getattr(ticker, "impliedVolatility", 0.25),
                }

                # 获取标的股票价格
                if hasattr(contract, "symbol"):
                    underlying_price = await self.get_current_price(contract.symbol)
                    option_details["underlying_price"] = underlying_price or 0.0

                # 取消市场数据订阅
                self.ib.cancelMktData(contract)

                return option_details

        except Exception as e:
            self.logger.error(f"Error getting option details: {e}")
            return None

    async def create_option_contract(
        self, symbol: str, option_type: str, strike: float, expiry: str
    ):
        """创建期权合约"""
        try:
            from ib_insync import Option

            # 转换期权类型
            right = "C" if option_type.upper() == "CALL" else "P"

            # 转换到期日格式
            if "-" in expiry:
                # 从YYYY-MM-DD转换为YYYYMMDD
                expiry = expiry.replace("-", "")

            # 创建期权合约
            contract = Option(symbol, expiry, strike, right, "SMART")

            # 验证合约
            qualified = await self.ib.qualifyContractsAsync(contract)
            if qualified:
                return qualified[0]
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error creating option contract: {e}")
            return None

    async def get_option_price(
        self, symbol: str, option_type: str, strike: float, expiry: str
    ) -> Optional[Dict]:
        """获取期权价格和Greeks"""
        try:
            # 创建期权合约
            contract = await self.create_option_contract(
                symbol, option_type, strike, expiry
            )
            if not contract:
                return None

            # 获取期权详情
            return await self.get_option_details(contract)

        except Exception as e:
            self.logger.error(f"Error getting option price: {e}")
            return None

    async def place_stop_loss_order(
        self, symbol: str, quantity: int, stop_price: float, action: str = "SELL"
    ) -> Optional[Order]:
        """
        Place a stop loss order

        参数:
        -----------
        symbol: str
            股票代码
        quantity: int
            Number of shares
        stop_price: float
            Stop loss trigger price
        action: str
            Order action (usually "SELL" for stop loss)
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            # 创建合约
            contract = Stock(symbol, "SMART", "USD")
            await self.ib.qualifyContractsAsync(contract)

            # 创建止损订单
            order = StopOrder(action, quantity, stop_price)

            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} stop loss order for {quantity} shares of {symbol} at ${stop_price}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing stop loss order for {symbol}: {e}")
            return None

    async def modify_stop_loss_order(self, symbol: str, new_stop_price: float) -> bool:
        """
        Modify existing stop loss order for a symbol

        参数:
        -----------
        symbol: str
            股票代码
        new_stop_price: float
            New stop loss trigger price
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return False

        try:
            # 获取所有未结订单
            trades = self.ib.openTrades()

            # 查找该股票的止损订单
            for trade in trades:
                if (
                    trade.contract.symbol == symbol
                    and hasattr(trade.order, "auxPrice")
                    and trade.order.orderType == "STP"
                ):
                    # Modify the order
                    trade.order.auxPrice = new_stop_price
                    self.ib.placeOrder(trade.contract, trade.order)

                    self.logger.info(
                        f"Modified stop loss for {symbol} to ${new_stop_price}"
                    )
                    return True

            self.logger.warning(f"No stop loss order found for {symbol}")
            return False

        except Exception as e:
            self.logger.error(f"Error modifying stop loss for {symbol}: {e}")
            return False

    async def get_options_chain(
        self, symbol: str, expiry_date: str = None
    ) -> Optional[list]:
        """
        Get options chain for a symbol - improved version

        参数:
        -----------
        symbol: str
            股票代码
        expiry_date: str
            Expiry date in YYYY-MM-DD format (optional)
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            # Create stock contract first
            stock = Stock(symbol, "SMART", "USD")
            qualified_stocks = await self.ib.qualifyContractsAsync(stock)

            if not qualified_stocks:
                self.logger.error(f"Cannot qualify stock contract for {symbol}")
                return None

            stock = qualified_stocks[0]

            # Get option chain parameters
            chains = await self.ib.reqSecDefOptParamsAsync(
                stock.symbol, "", stock.secType, stock.conId
            )

            if not chains:
                self.logger.warning(f"No options chain found for {symbol}")
                return None

            # Get the first chain (usually the most liquid exchange)
            chain = chains[0]
            self.logger.info(
                f"找到{symbol}的期权链: {len(chain.expirations)}个到期日，{len(chain.strikes)}个行权价"
            )

            # Filter expiries - get next 2-3 monthly expiries
            expiries = sorted(chain.expirations)[:3]
            if expiry_date:
                target_expiry = expiry_date.replace("-", "")
                expiries = [exp for exp in expiries if exp == target_expiry]

            # Filter strikes - get reasonable range around current price
            current_price = await self.get_current_price(symbol)
            if current_price:
                # Get strikes within ±20% of current price
                min_strike = current_price * 0.8
                max_strike = current_price * 1.2
                strikes = [s for s in chain.strikes if min_strike <= s <= max_strike]
            else:
                # If no current price, use middle range of strikes
                strikes = sorted(chain.strikes)[
                    len(chain.strikes) // 4 : 3 * len(chain.strikes) // 4
                ]

            self.logger.info(
                f"使用{len(expiries)}个到期日和{len(strikes)}个行权价"
            )

            # Create option contracts - 使用SMART交易所避免Error 200
            options = []
            for expiry in expiries:
                for strike in strikes[:20]:  # Limit strikes to avoid too many requests
                    # 使用SMART交易所而不是特定交易所，提高成功率
                    call = Option(symbol, expiry, strike, "C", "SMART")
                    put = Option(symbol, expiry, strike, "P", "SMART")
                    options.extend([call, put])

            # Qualify contracts in batches to avoid rate limits
            qualified_options = []
            batch_size = 50

            for i in range(0, len(options), batch_size):
                batch = options[i : i + batch_size]
                try:
                    qualified_batch = await self.ib.qualifyContractsAsync(*batch)
                    # 过滤掉无效合约（避免Error 200）
                    valid_contracts = [c for c in qualified_batch if c.conId > 0]
                    qualified_options.extend(valid_contracts)

                    if len(valid_contracts) < len(qualified_batch):
                        invalid_count = len(qualified_batch) - len(valid_contracts)
                        self.logger.debug(f"⚠️ Filtered out {invalid_count} invalid contracts in batch {i // batch_size + 1}")

                    await asyncio.sleep(0.5)  # Rate limiting
                except Exception as e:
                    self.logger.warning(
                        f"Failed to qualify batch {i // batch_size + 1}: {e}"
                    )

            self.logger.info(
                f"成功确认{symbol}的{len(qualified_options)}个期权"
            )
            return qualified_options

        except Exception as e:
            self.logger.error(f"Error getting options chain for {symbol}: {e}")
            return None

    async def place_options_order(
        self,
        symbol: str,
        expiry: str,
        strike: float,
        right: str,
        quantity: int,
        action: str = "BUY",
        order_type: str = "MKT",
        limit_price: float = None,
    ) -> Optional[Order]:
        """
        Place an options order

        参数:
        -----------
        symbol: str
            股票代码
        expiry: str
            Expiry date in YYYYMMDD format
        strike: float
            Strike price
        right: str
            "C" for Call, "P" for Put
        quantity: int
            Number of contracts
        action: str
            "BUY" or "SELL"
        order_type: str
            "MKT" for market, "LMT" for limit
        limit_price: float
            Limit price (required for limit orders)
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            # Create option contract
            contract = Option(symbol, expiry, strike, right, "SMART")

            # 验证合约是否有效
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)
            if not qualified_contracts:
                self.logger.error(f"❌ Invalid option contract: {contract}")
                return None

            contract = qualified_contracts[0]

            # Create order
            if order_type == "MKT":
                order = MarketOrder(action, quantity)
            elif order_type == "LMT":
                if limit_price is None:
                    raise ValueError("Limit price required for limit orders")
                order = LimitOrder(action, quantity, limit_price)
            else:
                raise ValueError(f"Unsupported order type: {order_type}")

            trade = self.ib.placeOrder(contract, order)

            option_desc = f"{right} {symbol} {expiry} {strike}"
            self.logger.info(
                f"Placed {action} {order_type} order for {quantity} {option_desc} contracts"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing options order: {e}")
            return None

    async def get_options_market_data(self, contracts: list) -> Dict:
        """
        Get market data for options contracts

        参数:
        -----------
        contracts: list
            List of option contracts
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return {}

        try:
            # Request market data for all contracts
            tickers = []
            for contract in contracts[:20]:  # Limit to avoid rate limits
                ticker = self.ib.reqMktData(contract)
                tickers.append(ticker)

            # Wait for data
            await asyncio.sleep(2)

            market_data = {}
            for ticker in tickers:
                if ticker.contract:
                    # 尝试获取价格数据，优先级：last > close > mid > 模拟价格
                    price = None
                    if ticker.last and ticker.last > 0:
                        price = ticker.last
                    elif ticker.close and ticker.close > 0:
                        price = ticker.close
                    elif (
                        ticker.bid and ticker.ask and ticker.bid > 0 and ticker.ask > 0
                    ):
                        price = (ticker.bid + ticker.ask) / 2
                    else:
                        # 如果没有市场数据，使用基于内在价值的模拟价格
                        price = self._estimate_option_price(ticker.contract)

                    if price and price > 0:
                        key = f"{ticker.contract.symbol}_{ticker.contract.lastTradeDateOrContractMonth}_{ticker.contract.strike}_{ticker.contract.right}"
                        market_data[key] = {
                            "symbol": ticker.contract.symbol,
                            "expiry": ticker.contract.lastTradeDateOrContractMonth,
                            "strike": ticker.contract.strike,
                            "right": ticker.contract.right,
                            "last_price": price,
                            "bid": ticker.bid
                            if ticker.bid and ticker.bid > 0
                            else price * 0.95,
                            "ask": ticker.ask
                            if ticker.ask and ticker.ask > 0
                            else price * 1.05,
                            "volume": ticker.volume
                            if ticker.volume
                            else 100,  # 模拟交易量
                            "open_interest": getattr(
                                ticker, "openInterest", 500
                            ),  # 模拟持仓量
                        }

            self.logger.info(f"检索到{len(market_data)}个期权的市场数据")
            return market_data

        except Exception as e:
            self.logger.error(f"Error getting options market data: {e}")
            return {}

    def _estimate_option_price(self, contract) -> float:
        """估算期权价格（当没有市场数据时）"""
        try:
            # 基于内在价值和时间价值的简单估算
            strike = contract.strike

            # 假设标的股票价格（实际应该获取真实价格）
            underlying_price = 100.0  # 简化假设

            # 计算内在价值
            if contract.right == "C":  # Call
                intrinsic_value = max(0, underlying_price - strike)
            else:  # Put
                intrinsic_value = max(0, strike - underlying_price)

            # 添加时间价值（简化估算）
            time_value = 2.0  # 固定时间价值

            estimated_price = intrinsic_value + time_value

            # 确保最小价格
            return max(0.5, estimated_price)

        except Exception as e:
            self.logger.warning(f"Error estimating option price: {e}")
            return 2.0  # 默认价格

    async def get_current_price(self, symbol: str) -> Optional[float]:
        """
        获取当前股票价格 - 处理市场关闭情况

        参数:
        -----------
        symbol: str
            股票代码
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)

            if not qualified_contracts:
                self.logger.error(f"Cannot qualify contract for {symbol}")
                return None

            contract = qualified_contracts[0]

            # Try to get real-time data first
            ticker = self.ib.reqMktData(contract)
            await asyncio.sleep(3)  # Wait longer for data

            price = None

            # 价格数据的优先级顺序 - 安全获取属性值
            def safe_get_price(attr_value):
                """安全获取价格属性值"""
                if attr_value is None or attr_value == -1 or attr_value == 0:
                    return None
                try:
                    # 如果是方法，调用它；如果是属性，直接返回
                    if callable(attr_value):
                        return float(attr_value())
                    else:
                        return float(attr_value)
                except (TypeError, ValueError):
                    return None

            last_price = safe_get_price(ticker.last)
            close_price = safe_get_price(ticker.close)
            bid_price = safe_get_price(ticker.bid)
            ask_price = safe_get_price(ticker.ask)
            market_price = safe_get_price(ticker.marketPrice)

            if last_price and last_price > 0:
                price = last_price
                price_type = "last"
            elif close_price and close_price > 0:
                price = close_price
                price_type = "close"
            elif bid_price and ask_price and bid_price > 0 and ask_price > 0:
                price = (bid_price + ask_price) / 2
                price_type = "mid"
            elif market_price and market_price > 0:
                price = market_price
                price_type = "market"

            # 如果没有实时数据，尝试历史数据
            if not price:
                self.logger.info(
                    f"{symbol}没有实时数据，尝试历史数据"
                )
                try:
                    end_date = datetime.now() - timedelta(days=5)  # Last 5 days

                    bars = await self.ib.reqHistoricalDataAsync(
                        contract,
                        endDateTime=end_date,
                        durationStr="5 D",
                        barSizeSetting="1 day",
                        whatToShow="TRADES",
                        useRTH=True,
                    )

                    if bars:
                        price = bars[-1].close  # Last close price
                        price_type = "historical_close"

                except Exception as hist_e:
                    self.logger.warning(f"Historical data request failed: {hist_e}")

            # 清理
            try:
                self.ib.cancelMktData(contract)
            except Exception:
                pass

            if price and price > 0:
                self.logger.info(f"{symbol}的价格: ${price:.2f} ({price_type})")
                return price
            else:
                self.logger.warning(f"No valid price data available for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None


# 全局客户端管理器实例
_client_manager = IBKRClientManager()


def get_ibkr_client(purpose: str = "default", config=None) -> IBKRClient:
    """
    获取IBKR客户端实例的便利函数

    Args:
        purpose: 客户端用途 ('default', 'trading', 'data', 'options', 'updater')
        config: IBKR配置

    Returns:
        IBKRClient实例
    """
    return _client_manager.get_client(purpose, config)


def disconnect_all_ibkr_clients():
    """断开所有IBKR客户端连接的便利函数"""
    _client_manager.disconnect_all()
