#!/usr/bin/env python
"""
核心工具函数模块

提供系统通用的工具函数，包括货币转换、数据格式转换、
数学计算等基础功能。为其他模块提供底层支持功能。
"""

import logging
from typing import List, Union

import numpy as np

logger = logging.getLogger(__name__)


def extract_hierarchical_info(sectors: dict, industries: dict) -> dict:
    """
    提取行业和板块信息，用于构建概率模型

    参数
    ----------
    sectors: dict
        股票级别的板块字典
    industries: dict
        股票级别的行业字典

    返回
    -------
    返回包含以下键的字典:
    - num_sectors: 唯一板块数量
    - num_industries: 唯一行业数量
    - sectors_id: 股票级别的索引列表，对应其所属板块
    - industries_id: 股票级别的索引列表，对应其所属行业
    - sector_industries_id: 行业级别的索引列表，对应其所属板块
    - unique_sectors: 唯一板块名称数组
    - unique_industries: 唯一行业名称数组
    """
    # 处理空输入的情况
    if not sectors or not industries:
        logger.warning("空的板块或行业数据，使用默认值")
        return dict(
            num_stocks=0,
            num_sectors=1,
            num_industries=1,
            sectors_id=[0],
            industries_id=[0],
            sector_industries_id=[0],
            unique_sectors=np.array(["Unknown"]),
            unique_industries=np.array(["Unknown"]),
        )

    # 查找唯一的板块名称
    usectors = np.unique(list(sectors.values()))
    num_sectors = len(usectors)

    # 处理空的unique sectors
    if num_sectors == 0:
        logger.warning("未找到唯一板块，使用默认值")
        usectors = np.array(["Unknown"])
        num_sectors = 1

    # 提供股票级别的板块ID
    sectors_id = []
    for sector in sectors.values():
        matches = np.where(usectors == sector)[0]
        if len(matches) > 0:
            sectors_id.append(matches[0])
        else:
            sectors_id.append(0)  # 默认到第一个sector

    # 查找唯一的行业名称并存储索引
    uindustries, industries_idx = np.unique(
        list(industries.values()), return_index=True
    )
    num_industries = len(uindustries)

    # 处理空的unique industries
    if num_industries == 0:
        logger.warning("未找到唯一行业，使用默认值")
        uindustries = np.array(["Unknown"])
        industries_idx = np.array([0])
        num_industries = 1

    # 提供股票级别的行业ID
    industries_id = []
    for industry in industries.values():
        matches = np.where(uindustries == industry)[0]
        if len(matches) > 0:
            industries_id.append(matches[0])
        else:
            industries_id.append(0)  # 默认到第一个industry

    # 提供行业级别的板块ID
    if len(sectors_id) > 0 and len(industries_idx) > 0:
        sector_industries_id = np.array(sectors_id)[industries_idx].tolist()
    else:
        sector_industries_id = [0]

    # 将相关信息放入字典
    return dict(
        num_stocks=len(sectors),
        num_sectors=num_sectors,
        num_industries=num_industries,
        industries_id=industries_id,
        sectors_id=sectors_id,
        sector_industries_id=sector_industries_id,
        unique_sectors=usectors,
        unique_industries=uindustries,
    )


def calculate_correlation(
    data1: Union[List, np.ndarray], data2: Union[List, np.ndarray]
) -> float:
    """
    计算两个数据序列的相关性

    参数:
        data1: 第一个数据序列
        data2: 第二个数据序列

    返回:
        相关系数 (-1到1之间)
    """
    try:
        # 转换为numpy数组
        arr1 = np.array(data1)
        arr2 = np.array(data2)

        # 检查数据有效性
        if len(arr1) != len(arr2) or len(arr1) == 0:
            return 0.0

        # 移除NaN值和无穷值
        mask = ~(np.isnan(arr1) | np.isnan(arr2) | np.isinf(arr1) | np.isinf(arr2))
        if np.sum(mask) < 3:  # 至少需要3个数据点才能计算有意义的相关性
            return 0.0

        arr1_clean = arr1[mask]
        arr2_clean = arr2[mask]

        # 检查数据变异性（避免常数序列）
        if np.std(arr1_clean) == 0 or np.std(arr2_clean) == 0:
            return 0.0

        # 使用更安全的相关性计算方法
        with np.errstate(divide='ignore', invalid='ignore'):
            correlation_matrix = np.corrcoef(arr1_clean, arr2_clean)

        # 检查计算结果
        if correlation_matrix.shape != (2, 2):
            return 0.0

        correlation = correlation_matrix[0, 1]

        # 处理NaN或无穷结果
        if np.isnan(correlation) or np.isinf(correlation):
            return 0.0

        return float(correlation)

    except Exception as e:
        print(f"Error calculating correlation: {e}")
        return 0.0
