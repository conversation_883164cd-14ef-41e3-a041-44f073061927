#!/usr/bin/env python
"""
统一配置管理模块

智能股票交易系统的集中配置管理，整合所有模块的配置参数。
包含IBKR连接、数据下载、模型训练、交易策略、风险管理、期权交易等配置。
支持环境变量配置、多种预设模式和统一验证。
"""

import os
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union
from enum import Enum


# ============================================================================
# 基础枚举类型 (Base Enums)
# ============================================================================

class TradingMode(Enum):
    """交易模式"""
    PAPER = "paper"  # 纸上交易
    LIVE = "live"    # 实盘交易


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ModelType(Enum):
    """模型类型"""
    LSTM = "lstm"
    CNN_GRU = "cnn_gru"
    MSIS_MCS = "msis_mcs"
    ENSEMBLE = "ensemble"


class ExtremeLevel(Enum):
    """极值水平"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


class ExitReason(Enum):
    """期权出场原因枚举"""
    TAKE_PROFIT_25PCT = "TAKE_PROFIT_25PCT"
    TAKE_PROFIT_50PCT = "TAKE_PROFIT_50PCT"
    TAKE_PROFIT_75PCT = "TAKE_PROFIT_75PCT"
    STOP_LOSS_25PCT = "STOP_LOSS_25PCT"
    STOP_LOSS_50PCT = "STOP_LOSS_50PCT"
    STOP_LOSS_75PCT = "STOP_LOSS_75PCT"
    TIME_DECAY_EXIT = "TIME_DECAY_EXIT"
    DELTA_RISK_EXIT = "DELTA_RISK_EXIT"
    IV_COLLAPSE_EXIT = "IV_COLLAPSE_EXIT"
    CORRELATION_BREAKDOWN = "CORRELATION_BREAKDOWN"
    MARKET_VOLATILITY_EXIT = "MARKET_VOLATILITY_EXIT"


class ExitUrgency(Enum):
    """期权出场紧急程度"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


# ============================================================================
# 核心配置类 (Core Configuration Classes)
# ============================================================================

@dataclass
class IBKRConfig:
    """Interactive Brokers 连接配置"""
    
    # 连接设置
    host: str = "127.0.0.1"
    port: int = 7497  # 7497=纸上交易，7496=实盘交易
    client_id: Optional[int] = None  # 自动分配
    timeout: int = 30
    
    # 交易设置
    paper_trading: bool = True
    max_position_size: float = 10000.0
    max_daily_trades: int = 50
    
    # 风险管理
    max_portfolio_risk: float = 0.02
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.10
    max_sector_exposure: float = 0.3
    max_single_position: float = 0.05
    
    # 数据设置
    use_ibkr_data: bool = True
    fallback_to_yahoo: bool = True
    data_duration_years: int = 3
    
    # 性能设置
    max_concurrent_requests: int = 50
    max_requests_per_10min: int = 60
    enable_data_cache: bool = True
    cache_ttl_seconds: int = 300
    enable_adaptive_concurrent: bool = True
    enable_prefetch: bool = True
    
    # 日志设置
    log_level: str = "INFO"
    log_file: str = "ibkr_trading.log"


@dataclass
class ModelConfig:
    """机器学习模型配置"""
    
    # 模型选择
    model_type: str = "msis_mcs"  # lstm, cnn_gru, msis_mcs, ensemble
    enable_ensemble: bool = True
    ensemble_models: List[str] = field(default_factory=lambda: ["lstm", "cnn_gru", "msis_mcs"])
    
    # 训练参数
    epochs: int = 200
    batch_size: int = 32
    learning_rate: float = 0.001
    early_stopping_patience: int = 30
    validation_split: float = 0.2
    
    # 数据处理
    sequence_length: int = 60
    prediction_horizon: int = 1
    feature_scaling: str = "minmax"  # minmax, standard, robust
    
    # 模型架构
    lstm_units: int = 50
    dropout_rate: float = 0.2
    dense_units: int = 25
    
    # 性能优化
    use_gpu: bool = True
    mixed_precision: bool = False
    enable_tensorboard: bool = True


@dataclass
class TradingConfig:
    """交易策略配置"""
    
    # 基础设置
    initial_capital: float = 100000.0
    max_positions: int = 30
    position_size_ratio: float = 1/30
    
    # 信号配置
    extreme_level: str = "high"  # low, medium, high, extreme
    trend_threshold: float = 0.05
    correlation_threshold: float = 0.7
    
    # 风险管理
    max_rel_loss: float = 0.05
    min_rel_profit: float = 0.10
    hold_period_min: int = 1
    hold_period_max: int = 30
    
    # 机器人配置
    enable_bots: List[str] = field(default_factory=lambda: ["Adam", "Betty", "Chris"])
    bot_capital_allocation: Dict[str, float] = field(default_factory=lambda: {
        "Adam": 0.3, "Betty": 0.3, "Chris": 0.2, "Dany": 0.1, "Eddy": 0.05, "Flora": 0.05
    })


@dataclass
class ExitCondition:
    """期权出场条件定义"""
    reason: ExitReason
    threshold: float
    urgency: ExitUrgency
    description: str
    auto_execute: bool = False


@dataclass
class OptionsConfig:
    """期权交易配置"""

    # 基础设置
    enable_options_trading: bool = True
    max_option_positions: int = 10
    max_risk_per_trade: float = 0.015

    # 期权选择优化
    min_time_to_expiry: int = 15
    max_time_to_expiry: int = 45
    optimal_time_to_expiry: int = 30
    optimal_moneyness_call: float = 1.04
    optimal_moneyness_put: float = 1.04
    max_moneyness_deviation: float = 0.03

    # 流动性要求
    min_daily_volume: int = 1500
    min_open_interest: int = 150
    max_bid_ask_spread: float = 0.10
    min_option_price: float = 0.75

    # 隐含波动率
    min_implied_volatility: float = 0.15
    max_implied_volatility: float = 0.50
    optimal_implied_volatility: float = 0.28

    # 希腊字母风险
    max_delta_exposure: float = 0.90
    min_delta_exposure: float = 0.10
    max_theta_decay_daily: float = 0.10
    max_vega_risk: float = 0.50

    # 策略控制
    min_correlation: float = 0.01
    min_divergence: float = 0.1
    max_strategies: int = 3
    max_same_underlying: int = 1
    correlation_confidence_threshold: float = 0.8

    # 出场策略
    profit_taking_levels: Dict[str, float] = field(default_factory=lambda: {
        "conservative": 0.25, "moderate": 0.50, "aggressive": 0.75
    })
    stop_loss_levels: Dict[str, float] = field(default_factory=lambda: {
        "tight": 0.25, "moderate": 0.50, "loose": 0.75
    })

    # 时间管理
    time_decay_exit_days: int = 7
    weekend_exit_threshold: int = 3

    # 相关性监控
    correlation_breakdown_threshold: float = 0.30
    correlation_check_period: int = 5

    # 市场条件
    market_volatility_threshold: float = 0.40
    max_market_drawdown: float = 0.05

    # 自动执行设置
    auto_execute_high_urgency: bool = True
    auto_execute_critical: bool = True
    require_confirmation_medium: bool = True

    # 出场订单设置
    exit_order_type: str = "LMT"
    limit_price_buffer: float = 0.02
    max_exit_attempts: int = 3
    exit_timeout_seconds: int = 300

    # 风险管理
    max_options_allocation: float = 0.08
    max_single_strategy_risk: float = 0.015
    max_sector_options_exposure: float = 0.04
    options_stop_loss: float = 0.50
    time_based_stop_loss: int = 7
    max_market_volatility: float = 0.35
    min_market_liquidity_score: float = 0.7

    # 性能优化
    enable_options_data_cache: bool = True
    cache_expiry_minutes: int = 5
    max_concurrent_option_requests: int = 10
    enable_parallel_scoring: bool = True
    batch_size_option_analysis: int = 50
    enable_real_time_monitoring: bool = True
    monitoring_interval_seconds: int = 30

    # 动态风险管理
    enable_dynamic_risk: bool = True
    volatility_risk_adjustment: bool = True
    volatility_adjustment_factors: Dict[str, float] = field(default_factory=lambda: {
        "low_vol": 1.2, "normal_vol": 1.0, "high_vol": 0.7, "extreme_vol": 0.4
    })

    # 评分权重
    scoring_weights: Dict[str, float] = field(default_factory=lambda: {
        "liquidity": 0.30, "divergence": 0.25, "confidence": 0.15,
        "spread": 0.12, "moneyness": 0.10, "volatility": 0.05, "time_decay": 0.03
    })


@dataclass
class SystemConfig:
    """系统配置"""
    
    # 文件路径
    data_dir: str = "data"
    results_dir: str = "results"
    logs_dir: str = "logs"
    cache_dir: str = "data/cache"
    
    # 数据文件
    stock_info_file: str = "data/stock_info/stock_info.csv"
    symbols_file: str = "data/stock_info/symbols_list.txt"
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file_max_size: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 5
    
    # 性能设置
    max_workers: int = 4
    memory_limit_gb: float = 8.0
    enable_multiprocessing: bool = True
    
    # 监控设置
    enable_performance_monitoring: bool = True
    monitoring_interval: int = 60  # 秒
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "memory_usage": 0.8,
        "cpu_usage": 0.9,
        "error_rate": 0.05
    })


# ============================================================================
# 统一配置类 (Unified Configuration Class)
# ============================================================================

@dataclass
class UnifiedConfig:
    """统一配置管理类"""
    
    # 核心配置组件
    ibkr: IBKRConfig = field(default_factory=IBKRConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    options: OptionsConfig = field(default_factory=OptionsConfig)
    system: SystemConfig = field(default_factory=SystemConfig)
    
    # 全局设置
    trading_mode: str = "paper"  # paper, live
    debug_mode: bool = False
    dry_run: bool = False
    
    def __post_init__(self):
        """初始化后验证配置"""
        self.validate_config()
        self.setup_directories()
        self.setup_logging()
    
    def validate_config(self):
        """验证配置参数"""
        errors = []

        # 验证IBKR配置
        if self.ibkr.port not in [7496, 7497]:
            errors.append("IBKR端口必须是7496(实盘)或7497(纸上交易)")

        if self.ibkr.max_position_size <= 0:
            errors.append("最大仓位金额必须大于0")

        # 验证交易配置
        if self.trading.initial_capital <= 0:
            errors.append("初始资金必须大于0")

        if sum(self.trading.bot_capital_allocation.values()) > 1.0:
            errors.append("机器人资金分配总和不能超过100%")

        # 验证期权配置
        if self.options.min_time_to_expiry >= self.options.max_time_to_expiry:
            errors.append("期权最小到期时间必须小于最大到期时间")

        if self.options.min_implied_volatility >= self.options.max_implied_volatility:
            errors.append("期权最小隐含波动率必须小于最大隐含波动率")

        if self.options.max_risk_per_trade <= 0 or self.options.max_risk_per_trade > 0.1:
            errors.append("期权每笔交易风险必须在0-10%之间")

        if self.options.min_correlation < 0 or self.options.min_correlation > 1:
            errors.append("最小相关系数必须在0-1之间")

        # 验证评分权重总和
        if abs(sum(self.options.scoring_weights.values()) - 1.0) > 0.01:
            errors.append("期权评分权重总和必须等于1.0")

        if errors:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    def setup_directories(self):
        """创建必要的目录"""
        directories = [
            self.system.data_dir,
            self.system.results_dir,
            self.system.logs_dir,
            self.system.cache_dir,
            "data/stock_info",
            "data/models",
            "data/cache"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=getattr(logging, self.system.log_level),
            format=self.system.log_format,
            handlers=[
                logging.FileHandler(f"{self.system.logs_dir}/system.log"),
                logging.StreamHandler()
            ]
        )


# ============================================================================
# 预设配置 (Preset Configurations)
# ============================================================================

def get_conservative_config() -> UnifiedConfig:
    """保守配置 - 适合新手和纸上交易"""
    config = UnifiedConfig()
    
    # 保守的IBKR设置
    config.ibkr.paper_trading = True
    config.ibkr.max_position_size = 5000.0
    config.ibkr.max_daily_trades = 20
    config.ibkr.stop_loss_pct = 0.03
    config.ibkr.take_profit_pct = 0.08
    
    # 保守的交易设置
    config.trading.max_positions = 20
    config.trading.max_rel_loss = 0.03
    config.trading.extreme_level = "high"
    
    # 禁用期权交易
    config.options.enable_options_trading = False
    
    return config


def get_aggressive_config() -> UnifiedConfig:
    """激进配置 - 适合有经验的交易者"""
    config = UnifiedConfig()
    
    # 激进的IBKR设置
    config.ibkr.max_position_size = 20000.0
    config.ibkr.max_daily_trades = 100
    config.ibkr.stop_loss_pct = 0.08
    config.ibkr.take_profit_pct = 0.15
    
    # 激进的交易设置
    config.trading.max_positions = 50
    config.trading.extreme_level = "extreme"
    config.trading.enable_bots = ["Adam", "Betty", "Chris", "Dany", "Eddy", "Flora"]
    
    # 启用期权交易
    config.options.enable_options_trading = True
    config.options.max_option_positions = 20
    config.options.max_risk_per_trade = 0.025
    
    return config


def get_production_config() -> UnifiedConfig:
    """生产配置 - 适合实盘交易"""
    config = UnifiedConfig()
    
    # 生产环境设置
    config.ibkr.paper_trading = False
    config.ibkr.port = 7496  # 实盘端口
    config.trading_mode = "live"
    config.system.log_level = "WARNING"
    
    # 严格的风险控制
    config.ibkr.max_portfolio_risk = 0.01
    config.trading.max_rel_loss = 0.03
    config.options.max_risk_per_trade = 0.01
    
    return config


# ============================================================================
# 默认期权出场条件 (Default Options Exit Conditions)
# ============================================================================

DEFAULT_EXIT_CONDITIONS = [
    # 止盈条件
    ExitCondition(
        reason=ExitReason.TAKE_PROFIT_25PCT,
        threshold=0.25,
        urgency=ExitUrgency.MEDIUM,
        description="25%止盈出场",
        auto_execute=False,
    ),
    ExitCondition(
        reason=ExitReason.TAKE_PROFIT_50PCT,
        threshold=0.50,
        urgency=ExitUrgency.MEDIUM,
        description="50%止盈出场",
        auto_execute=True,
    ),
    ExitCondition(
        reason=ExitReason.TAKE_PROFIT_75PCT,
        threshold=0.75,
        urgency=ExitUrgency.HIGH,
        description="75%止盈出场",
        auto_execute=True,
    ),
    # 止损条件
    ExitCondition(
        reason=ExitReason.STOP_LOSS_25PCT,
        threshold=-0.25,
        urgency=ExitUrgency.MEDIUM,
        description="25%止损出场",
        auto_execute=False,
    ),
    ExitCondition(
        reason=ExitReason.STOP_LOSS_50PCT,
        threshold=-0.50,
        urgency=ExitUrgency.HIGH,
        description="50%止损出场",
        auto_execute=True,
    ),
    ExitCondition(
        reason=ExitReason.STOP_LOSS_75PCT,
        threshold=-0.75,
        urgency=ExitUrgency.CRITICAL,
        description="75%止损出场",
        auto_execute=True,
    ),
    # 时间和风险条件
    ExitCondition(
        reason=ExitReason.TIME_DECAY_EXIT,
        threshold=7,
        urgency=ExitUrgency.HIGH,
        description="时间价值衰减出场",
        auto_execute=True,
    ),
    ExitCondition(
        reason=ExitReason.CORRELATION_BREAKDOWN,
        threshold=0.30,
        urgency=ExitUrgency.MEDIUM,
        description="相关性破裂出场",
        auto_execute=False,
    ),
]


# ============================================================================
# 默认配置实例 (Default Configuration Instance)
# ============================================================================

# 创建默认配置实例
DEFAULT_CONFIG = UnifiedConfig()

# 向后兼容的配置实例
DEFAULT_IBKR_CONFIG = DEFAULT_CONFIG.ibkr
DEFAULT_MODEL_CONFIG = DEFAULT_CONFIG.model
DEFAULT_TRADING_CONFIG = DEFAULT_CONFIG.trading
DEFAULT_OPTIONS_CONFIG = DEFAULT_CONFIG.options
DEFAULT_SYSTEM_CONFIG = DEFAULT_CONFIG.system
DEFAULT_EXIT_CONFIG = DEFAULT_OPTIONS_CONFIG  # 期权出场配置别名


# ============================================================================
# 配置加载函数 (Configuration Loading Functions)
# ============================================================================

def load_config_from_env() -> UnifiedConfig:
    """从环境变量加载配置"""
    config = UnifiedConfig()
    
    # IBKR配置
    if os.getenv("IBKR_HOST"):
        config.ibkr.host = os.getenv("IBKR_HOST")
    if os.getenv("IBKR_PORT"):
        config.ibkr.port = int(os.getenv("IBKR_PORT"))
    if os.getenv("IBKR_PAPER_TRADING"):
        config.ibkr.paper_trading = os.getenv("IBKR_PAPER_TRADING").lower() == "true"
    
    # 交易配置
    if os.getenv("INITIAL_CAPITAL"):
        config.trading.initial_capital = float(os.getenv("INITIAL_CAPITAL"))
    if os.getenv("EXTREME_LEVEL"):
        config.trading.extreme_level = os.getenv("EXTREME_LEVEL")
    
    # 系统配置
    if os.getenv("LOG_LEVEL"):
        config.system.log_level = os.getenv("LOG_LEVEL")
    
    return config


def get_config(preset: str = "default") -> UnifiedConfig:
    """获取指定预设的配置"""
    if preset == "conservative":
        return get_conservative_config()
    elif preset == "aggressive":
        return get_aggressive_config()
    elif preset == "production":
        return get_production_config()
    elif preset == "env":
        return load_config_from_env()
    else:
        return DEFAULT_CONFIG


# ============================================================================
# 配置工具函数 (Configuration Utility Functions)
# ============================================================================

def get_exit_conditions_by_urgency(urgency: ExitUrgency) -> List[ExitCondition]:
    """根据紧急程度获取出场条件"""
    return [
        condition for condition in DEFAULT_EXIT_CONDITIONS
        if condition.urgency == urgency
    ]


def get_auto_execute_conditions() -> List[ExitCondition]:
    """获取自动执行的出场条件"""
    return [
        condition for condition in DEFAULT_EXIT_CONDITIONS
        if condition.auto_execute
    ]


def get_dynamic_risk_limit(
    market_volatility: float,
    strategy_confidence: float,
    days_to_expiry: int,
    config: Optional[OptionsConfig] = None
) -> float:
    """计算动态风险限制"""
    if config is None:
        config = DEFAULT_OPTIONS_CONFIG

    base_risk = config.max_risk_per_trade

    # 波动率调整
    if market_volatility > 0.35:
        vol_factor = config.volatility_adjustment_factors["extreme_vol"]
    elif market_volatility > 0.25:
        vol_factor = config.volatility_adjustment_factors["high_vol"]
    elif market_volatility < 0.15:
        vol_factor = config.volatility_adjustment_factors["low_vol"]
    else:
        vol_factor = config.volatility_adjustment_factors["normal_vol"]

    # 置信度调整
    confidence_factor = min(1.5, max(0.5, strategy_confidence * 1.8))

    # 时间调整
    if days_to_expiry < 10:
        time_factor = 0.5
    elif days_to_expiry > 25:
        time_factor = 0.8
    else:
        time_factor = 1.0

    # 综合风险限制
    dynamic_risk = base_risk * vol_factor * confidence_factor * time_factor

    return max(0.005, min(0.025, dynamic_risk))


def validate_strategy_quality(
    strategy_score: float,
    liquidity_score: float,
    correlation_confidence: float,
    config: Optional[OptionsConfig] = None
) -> bool:
    """验证策略质量"""
    if config is None:
        config = DEFAULT_OPTIONS_CONFIG

    min_score = 0.6
    min_liquidity = 0.7
    min_confidence = config.correlation_confidence_threshold

    return (
        strategy_score >= min_score
        and liquidity_score >= min_liquidity
        and correlation_confidence >= min_confidence
    )


def get_optimal_contract_selection_criteria(config: Optional[OptionsConfig] = None) -> Dict:
    """获取最佳合约选择标准"""
    if config is None:
        config = DEFAULT_OPTIONS_CONFIG

    return {
        "moneyness_range": {
            "call_min": config.optimal_moneyness_call - config.max_moneyness_deviation,
            "call_max": config.optimal_moneyness_call + config.max_moneyness_deviation,
            "put_min": config.optimal_moneyness_put - config.max_moneyness_deviation,
            "put_max": config.optimal_moneyness_put + config.max_moneyness_deviation,
        },
        "time_to_expiry": {
            "min": config.min_time_to_expiry,
            "optimal": config.optimal_time_to_expiry,
            "max": config.max_time_to_expiry,
        },
        "implied_volatility": {
            "min": config.min_implied_volatility,
            "optimal": config.optimal_implied_volatility,
            "max": config.max_implied_volatility,
        },
        "liquidity": {
            "min_volume": config.min_daily_volume,
            "min_oi": config.min_open_interest,
            "max_spread": config.max_bid_ask_spread,
        },
    }


def get_config_summary(config: Optional[UnifiedConfig] = None) -> str:
    """获取配置摘要"""
    if config is None:
        config = DEFAULT_CONFIG

    return f"""
🎯 智能交易系统配置摘要
{"=" * 50}

📊 IBKR连接:
  • 主机: {config.ibkr.host}:{config.ibkr.port}
  • 纸上交易: {"是" if config.ibkr.paper_trading else "否"}
  • 最大仓位: ${config.ibkr.max_position_size:,.0f}

💰 交易策略:
  • 初始资金: ${config.trading.initial_capital:,.0f}
  • 最大仓位数: {config.trading.max_positions}
  • 极值水平: {config.trading.extreme_level}

🎯 期权交易:
  • 启用状态: {"是" if config.options.enable_options_trading else "否"}
  • 最大期权仓位: {config.options.max_option_positions}
  • 每笔风险: {config.options.max_risk_per_trade * 100:.1f}%
  • 最佳到期时间: {config.options.optimal_time_to_expiry}天

🛡️ 风险管理:
  • 止损百分比: {config.ibkr.stop_loss_pct * 100:.0f}%
  • 止盈百分比: {config.ibkr.take_profit_pct * 100:.0f}%
  • 最大投资组合风险: {config.ibkr.max_portfolio_risk * 100:.1f}%

📈 模型配置:
  • 模型类型: {config.model.model_type}
  • 集成模式: {"启用" if config.model.enable_ensemble else "禁用"}
  • 训练轮数: {config.model.epochs}

🔧 系统设置:
  • 交易模式: {config.trading_mode}
  • 调试模式: {"启用" if config.debug_mode else "禁用"}
  • 日志级别: {config.system.log_level}
{"=" * 50}
"""
